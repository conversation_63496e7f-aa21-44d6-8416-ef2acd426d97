2025-08-12 15:01:35,467 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:01:35,508 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ebde8f3e-8e97-46fb-a5d9-7fc314347dbe'}}
2025-08-12 15:01:35,509 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:01:35,537 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:01:35,576 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '04d3041f-8f43-4ee3-9ea5-717f7ee0c4e8'}}
2025-08-12 15:01:35,577 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:36,175 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4ffd2244-241b-41ba-80cd-015780490c1a'}}
2025-08-12 15:01:36,176 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:01:36,178 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4c6103f4-1964-4cdb-9541-ce137153fa0f'}}
2025-08-12 15:01:36,179 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:36,185 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:36,187 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:36,779 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4395b8e1-2bf2-4e6a-a1ed-3d10c62db2bb'}}
2025-08-12 15:01:36,783 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:37,155 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'b3757a27-09a7-45e6-a83f-b961be4abe16'}}
2025-08-12 15:01:37,156 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:37,158 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5a8a1efd-ef2c-4f19-9192-e0f95ec9c8e1'}}
2025-08-12 15:01:37,160 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:37,555 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f39ed76e-e46a-452b-97ba-996880da1598'}}
2025-08-12 15:01:37,557 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:01:37,632 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '00ddc9f6-3ba2-4c41-9026-cbe64320b539'}}
2025-08-12 15:01:37,635 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:37,786 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:01:37,830 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5f3923f2-68c2-48bc-b412-d85e3be1fbd8'}}
2025-08-12 15:01:37,831 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:38,160 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:01:38,211 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '02fa59e6-d97f-459b-b451-b6e261fb993f'}}
2025-08-12 15:01:38,212 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:38,224 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b97cce5a-9b66-47eb-85a2-e06951634017'}}
2025-08-12 15:01:38,225 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:38,226 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '663999d8-a091-45c3-aa9d-e63f0edbbe94'}}
2025-08-12 15:01:38,227 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:01:38,242 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:38,243 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:39,201 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'b17a23f4-086e-4bce-99cd-2211084ea993'}}
2025-08-12 15:01:39,202 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:39,226 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:01:39,232 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:01:39,248 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:01:39,264 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '27e7debc-cd84-4c30-a91b-76c06e49d566'}}
2025-08-12 15:01:39,265 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:39,273 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '55e2abdb-a1d0-4c15-a3be-a8129778ff27'}}
2025-08-12 15:01:39,274 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:39,287 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:39,288 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:01:39,598 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '52aa888a-6a2e-4465-b6e4-943ecb272425'}}
2025-08-12 15:01:39,599 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:01:39,685 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c0297f6f-6ebb-4e82-8a15-ea620210a511'}}
2025-08-12 15:01:39,686 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:39,878 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '76fe8cf1-1440-423d-b1dd-2a8bce33dce2'}}
2025-08-12 15:01:39,879 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:40,208 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:01:40,251 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '9defdf48-69df-4351-a57e-af0064e530da'}}
2025-08-12 15:01:40,253 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:40,258 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '86b74f71-a67a-4988-9c27-5c082dbde74a'}}
2025-08-12 15:01:40,260 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:40,616 - INFO - 记录 718 无需翻译
2025-08-12 15:01:40,617 - INFO - 处理进度: 17/488
2025-08-12 15:01:40,617 - INFO - 开始翻译记录 ID: 717
2025-08-12 15:01:40,634 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:01:40,668 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6585012e-8fe5-4346-9511-7a4147d793f6'}}
2025-08-12 15:01:40,669 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:01:40,691 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:01:40,732 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ee042e02-5ba9-4d95-9974-d1352a121073'}}
2025-08-12 15:01:40,733 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:41,312 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3b9a887b-02e3-443d-88b2-91fa56a3543a'}}
2025-08-12 15:01:41,313 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:41,318 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0013be1c-5285-43b8-81bb-8996ceedfcfb'}}
2025-08-12 15:01:41,320 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:41,330 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:41,332 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:01:41,939 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '83c56a0f-58e1-4a28-8270-785bf8609a9f'}}
2025-08-12 15:01:41,942 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:42,302 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'a1d9c6c5-0ab0-40e2-b9fc-ff14a3fd5501'}}
2025-08-12 15:01:42,302 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ed21d112-9564-4de7-a8a2-2d9d8b62f77e'}}
2025-08-12 15:01:42,305 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:42,305 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:42,711 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a2354608-f1f4-4e5a-bd07-2857eb1ae34e'}}
2025-08-12 15:01:42,712 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:01:42,820 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b4e8a415-d188-49ac-946d-bf911a28a60e'}}
2025-08-12 15:01:42,822 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:42,947 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:01:42,993 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '13737ca4-8f3f-4c45-bb1a-3a71a7cfb8f3'}}
2025-08-12 15:01:42,994 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:43,310 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:01:43,372 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c0011b39-5979-4958-969b-97cabd922771'}}
2025-08-12 15:01:43,373 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:43,376 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '22618a4f-9bb1-4f97-b47e-4ef55584a775'}}
2025-08-12 15:01:43,377 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:43,384 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7a0f7c28-3927-4fd5-87b8-b46f7e90dd90'}}
2025-08-12 15:01:43,385 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:43,398 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:43,401 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:01:44,135 - INFO - ============================================================
2025-08-12 15:01:44,135 - INFO - 开始翻译 corpus_en 表中从 content 字段开始的所有字段
2025-08-12 15:01:44,135 - INFO - 开始时间: 2025-08-12 15:01:44.135454
2025-08-12 15:01:44,135 - INFO - ============================================================
2025-08-12 15:01:44,211 - INFO - 数据库连接成功
2025-08-12 15:01:44,211 - INFO - 重试配置: JSON字段=2 次, 其他字段=2 次; 超时=120s; 使用星火大模型翻译API
2025-08-12 15:01:44,343 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '9874b0d1-0780-47c6-8abd-69f4d77c9ee0'}}
2025-08-12 15:01:44,344 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:44,383 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:01:44,386 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:01:44,426 - INFO - 记录 712 无需翻译
2025-08-12 15:01:44,426 - INFO - 处理进度: 23/488
2025-08-12 15:01:44,426 - INFO - 开始翻译记录 ID: 711
2025-08-12 15:01:44,436 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'bdcf9404-dea1-4cf8-af8e-4e3027f1b4c9'}}
2025-08-12 15:01:44,436 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0891b6ca-61f8-4c8d-a3e4-574a1276d44e'}}
2025-08-12 15:01:44,437 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:44,437 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:44,445 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:01:44,536 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:44,537 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:01:44,766 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a0301d31-e431-4ea7-b073-4079cfa648aa'}}
2025-08-12 15:01:44,767 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:01:44,819 - INFO - 找到 5 条需要翻译的记录
2025-08-12 15:01:44,819 - INFO - 预览前 5 条需要翻译的记录：
2025-08-12 15:01:44,820 - INFO - ID: 733, 包含中文的字段: 
2025-08-12 15:01:44,821 - INFO - ID: 732, 包含中文的字段: 
2025-08-12 15:01:44,822 - INFO - ID: 731, 包含中文的字段: timesmidpoint_chart_content(JSON)
2025-08-12 15:01:44,822 - INFO - ID: 730, 包含中文的字段: timesmidpoint_chart_content(JSON)
2025-08-12 15:01:44,823 - INFO - ID: 729, 包含中文的字段: solararc_chart_content(JSON)
2025-08-12 15:01:44,878 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ae5c1651-ad37-42a7-85d0-e4e08518a991'}}
2025-08-12 15:01:44,879 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:45,042 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e8e584f1-63c0-4765-96eb-d0aac79b6704'}}
2025-08-12 15:01:45,043 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:45,349 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:01:45,386 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'cd6b00aa-1cde-4300-a059-67811917bffd'}}
2025-08-12 15:01:45,387 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:45,411 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5ad2d80a-a55c-4613-85ed-fdfe8b5ca450'}}
2025-08-12 15:01:45,412 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:45,767 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:01:45,809 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4a95ee9d-2fc1-4142-a172-b2002d37f7ca'}}
2025-08-12 15:01:45,810 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:01:45,882 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:01:45,926 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5ab8e33d-dd8b-407d-a48c-371e24a584a0'}}
2025-08-12 15:01:45,927 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:46,486 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9561e007-3d13-4e92-ae67-531aa59d7e68'}}
2025-08-12 15:01:46,486 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2d486071-b12b-4eb0-8b15-86b43ae7030d'}}
2025-08-12 15:01:46,488 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:46,488 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:46,571 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:46,572 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:01:47,089 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fd9616aa-cedb-46a8-a711-9612fe686ccf'}}
2025-08-12 15:01:47,090 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:47,439 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'e21cafe2-90a6-44c0-90af-1cf27cd4134e'}}
2025-08-12 15:01:47,440 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:47,457 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd769fd87-6c9c-4f1d-9f11-6c6f88674ce2'}}
2025-08-12 15:01:47,458 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:47,856 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '55edaa0b-14de-42ea-b614-a15d4fa9115b'}}
2025-08-12 15:01:47,858 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:01:47,975 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dd030433-538e-42de-8549-ce50b59c1034'}}
2025-08-12 15:01:47,977 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:48,091 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:01:48,145 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8bf2d692-d0eb-4e7d-be39-39c8bb302114'}}
2025-08-12 15:01:48,146 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:48,460 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:01:48,503 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '87f1a943-cc33-42d4-b5e4-20392f6b04d5'}}
2025-08-12 15:01:48,504 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:01:48,538 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fd6922b0-af47-4060-82aa-4b94a7f1ce40'}}
2025-08-12 15:01:48,539 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:48,541 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3ae3dbbe-7788-46ab-a6f7-18f826ed488e'}}
2025-08-12 15:01:48,542 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:48,617 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:48,617 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:01:49,244 - INFO - 翻译成功 - 字段: compositeThirprogr_chart_content, 原文长度: 500, 译文长度: 1711
2025-08-12 15:01:49,248 - INFO - 字段 compositeThirprogr_chart_content 翻译完成并通过验证
2025-08-12 15:01:49,480 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '2d0141bf-960b-46d0-a77a-740a56b41547'}}
2025-08-12 15:01:49,481 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:49,540 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:01:49,545 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:01:49,586 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '014da53e-d4d3-4f54-b9e4-c96a2907a5ec'}}
2025-08-12 15:01:49,586 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '660dd3ec-515e-406a-8fa9-ac490462351f'}}
2025-08-12 15:01:49,587 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:01:49,587 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:49,619 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:01:49,662 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:49,663 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:01:49,900 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '287d9a6b-a9f5-4792-a32b-98fa3104aa5f'}}
2025-08-12 15:01:49,901 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:01:50,013 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'bde058ae-ec85-483b-a269-5f929ddf632d'}}
2025-08-12 15:01:50,014 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:50,187 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7054f7e5-2078-4255-baa0-2f48d2be2857'}}
2025-08-12 15:01:50,188 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:50,251 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:01:50,487 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:01:50,530 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'f615dcc2-d5d6-4418-82f3-70d9320488e9'}}
2025-08-12 15:01:50,531 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:50,541 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '72d26eff-35cf-481b-b2b6-3e8e80a25042'}}
2025-08-12 15:01:50,542 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:01:50,904 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:01:50,944 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '20f3ceab-5386-432e-8437-344f3bda4185'}}
2025-08-12 15:01:50,945 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:51,016 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:01:51,073 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '856d5045-fd64-478b-b995-6c9d95c987cb'}}
2025-08-12 15:01:51,076 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:51,640 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dd6024fd-8cd8-4751-8010-c45953d52e7a'}}
2025-08-12 15:01:51,641 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd8eec7db-13aa-4c44-bf36-e587139a3848'}}
2025-08-12 15:01:51,641 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:01:51,641 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:51,703 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:51,704 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:01:52,238 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2c873fb6-c1ef-44be-9c58-40d5a08713e1'}}
2025-08-12 15:01:52,239 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:52,588 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '65b4b0fc-bd53-4633-9074-1cffcbcead61'}}
2025-08-12 15:01:52,588 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '05a0475b-a52b-405e-b29c-6af91010f92a'}}
2025-08-12 15:01:52,589 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:52,589 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:01:52,993 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '01b845c4-f4eb-47f7-982a-14e1e1ae08a7'}}
2025-08-12 15:01:52,996 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:53,131 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cc2a7172-92e3-45e9-aa29-0a5f5e3bce00'}}
2025-08-12 15:01:53,135 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:53,243 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:01:53,283 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4a5bb05c-d370-40f5-b118-e6daae2ee1ea'}}
2025-08-12 15:01:53,284 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:53,593 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:01:53,637 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0b7a932e-6704-45a3-bbc4-23217f6d88c9'}}
2025-08-12 15:01:53,638 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:53,692 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ddc3fe40-176d-41db-a580-9c50e77e2bfd'}}
2025-08-12 15:01:53,692 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '17d15073-043d-4b45-ba4b-87a02551b049'}}
2025-08-12 15:01:53,693 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:53,694 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:01:53,751 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:53,752 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:01:54,646 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'c46bf165-68b1-400b-8192-4dfaeeb5d1a9'}}
2025-08-12 15:01:54,648 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:54,694 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:01:54,694 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:01:54,759 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2b532253-bb55-405b-9ff3-6047771357e3'}}
2025-08-12 15:01:54,759 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1cdf7961-4052-47c4-90ad-b2b4dfa944c3'}}
2025-08-12 15:01:54,760 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:01:54,761 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:54,761 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:54,802 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:54,804 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:55,044 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd5d53632-22d0-4cf4-afca-4fcdf2b12b13'}}
2025-08-12 15:01:55,045 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:55,182 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3768873c-470b-405c-b550-40b93bcd7668'}}
2025-08-12 15:01:55,184 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:55,328 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a7039d6c-920b-4d2f-8250-f2a8200b47d0'}}
2025-08-12 15:01:55,329 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:55,649 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:01:55,680 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a29988ec-e489-48b4-9f93-0a652a85f196'}}
2025-08-12 15:01:55,682 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:55,687 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '4317afa3-6c2f-4d7e-8bf8-a7e7c14af86a'}}
2025-08-12 15:01:55,688 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:56,051 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:01:56,097 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '66c8fcb0-9f12-4018-a0ca-0783ebcfd904'}}
2025-08-12 15:01:56,100 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:56,189 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:01:56,245 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd72a7b70-df19-49e4-a5f1-caf8355e296b'}}
2025-08-12 15:01:56,249 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:56,815 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '22ac5324-cdf0-4d82-aef3-b653a076d8f2'}}
2025-08-12 15:01:56,815 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a74ac4fa-6f32-4edf-bb71-b99d7256f59a'}}
2025-08-12 15:01:56,817 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:56,818 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:56,849 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:56,850 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:57,375 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '96e8cbd4-292b-4e91-a216-b430a9530041'}}
2025-08-12 15:01:57,377 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:57,743 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '85a5f1d6-02d1-45b1-ad9a-678af41a2e62'}}
2025-08-12 15:01:57,746 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:57,753 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '7c1ba663-2541-468c-9336-846d69f7c218'}}
2025-08-12 15:01:57,756 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:58,153 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0b0d97a1-63d1-483f-bfc0-5c14f32ee020'}}
2025-08-12 15:01:58,154 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:58,302 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e32114a4-f94c-4a49-b0a8-a5f1779be0bb'}}
2025-08-12 15:01:58,303 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:01:58,382 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:01:58,422 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0ea310a1-965c-4f99-8343-c9bc99be2d39'}}
2025-08-12 15:01:58,422 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:58,748 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:01:58,800 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4385cc79-288f-437c-b5b0-295dbf2e0da6'}}
2025-08-12 15:01:58,801 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:58,864 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5632e053-3c5e-41ec-ad10-e4096e7d395c'}}
2025-08-12 15:01:58,865 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:58,886 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0c336904-dc26-40c9-b33a-94f3f774db09'}}
2025-08-12 15:01:58,887 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:58,903 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:58,904 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:59,813 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '9e99b507-ced5-4a6c-915a-287b36cc7580'}}
2025-08-12 15:01:59,814 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:01:59,866 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:01:59,888 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:01:59,905 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:01:59,912 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2f0e6ff1-0c2b-438e-b7c0-0cbd27475036'}}
2025-08-12 15:01:59,913 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:59,930 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '12431c58-d560-4cb9-917c-0458af59885b'}}
2025-08-12 15:01:59,931 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:01:59,946 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:01:59,947 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:00,206 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd8c8da6b-1a30-4bec-8283-c9dbd9b5d29a'}}
2025-08-12 15:02:00,207 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:00,346 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0fd4a417-0e8c-4bad-bebd-8ad5e3e31ada'}}
2025-08-12 15:02:00,347 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:00,479 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '96a7f83e-8ad1-4b84-abb8-4adcbd1dfb34'}}
2025-08-12 15:02:00,480 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:00,817 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:02:00,856 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f911adec-2325-4d5c-95eb-d07fe60247be'}}
2025-08-12 15:02:00,859 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:00,862 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '58db08a1-d881-48cf-9120-7bdf9a9a4728'}}
2025-08-12 15:02:00,863 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:01,212 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:02:01,253 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3480aa00-8840-4f3c-8bf4-8130078008c6'}}
2025-08-12 15:02:01,254 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:01,352 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:02:01,390 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '57587f40-8b14-4b6c-95c3-5f32dc453f17'}}
2025-08-12 15:02:01,391 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:01,955 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7f728a73-abec-48ae-b3a1-50879cf641a9'}}
2025-08-12 15:02:01,956 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:01,969 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0b5e0837-d7f5-4bc5-9055-7bf70818e1c3'}}
2025-08-12 15:02:01,970 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:01,991 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:01,992 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:02,524 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e362ab72-a0c5-4a8e-acbd-d1ad868a5bb9'}}
2025-08-12 15:02:02,525 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:02,915 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '38ec4378-b24d-448b-8872-bed52054137c'}}
2025-08-12 15:02:02,916 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:02,932 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '2499468b-2354-4296-bf59-d09ea77c6e46'}}
2025-08-12 15:02:02,933 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:03,303 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ba8876b8-ad8b-45fa-9bf4-4b25c5841234'}}
2025-08-12 15:02:03,304 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:03,435 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5cdf006d-153f-443c-bd07-beec53a0035d'}}
2025-08-12 15:02:03,436 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:03,525 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:02:03,567 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4ccc0607-bd34-4c05-8319-a218c80e2d8c'}}
2025-08-12 15:02:03,570 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:02:03,922 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:02:03,976 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e323f27e-1946-4f19-9e12-a0661d4dc013'}}
2025-08-12 15:02:03,977 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:03,997 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b556b9f1-5b8b-4fe5-aa69-a65f7307051d'}}
2025-08-12 15:02:03,998 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:04,015 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2fc84ed9-7451-480c-816d-c85848ffc3ce'}}
2025-08-12 15:02:04,016 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:04,047 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:04,048 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:04,978 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '1cd5e13b-9478-40e2-b354-ed04c903b7bf'}}
2025-08-12 15:02:04,979 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:04,999 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:02:05,018 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:02:05,034 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1ff6b735-02f1-4ac8-bea5-81fec8c77f6d'}}
2025-08-12 15:02:05,035 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:05,049 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:02:05,060 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '580b3e98-b83d-4322-8b28-996dc6c2d53b'}}
2025-08-12 15:02:05,061 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:05,087 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:05,088 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:05,379 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '52212bea-0bc5-4673-ae55-0336f681d479'}}
2025-08-12 15:02:05,383 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:05,490 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8bf15c4c-c1fd-4992-88d4-8e3feb7024e9'}}
2025-08-12 15:02:05,493 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:05,608 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '92024958-9f03-4674-ab33-39e52461b341'}}
2025-08-12 15:02:05,609 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:02:05,980 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:02:06,016 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'f7567155-3d96-4deb-9c43-e403488957b7'}}
2025-08-12 15:02:06,017 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b86d9f30-1880-4758-aa3b-22a702dfb885'}}
2025-08-12 15:02:06,017 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:06,018 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:06,389 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:02:06,433 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b01c821b-d5c2-4ad9-9a36-be6e434fdffa'}}
2025-08-12 15:02:06,434 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:06,496 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:02:06,541 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd6b13b88-8f06-4f5b-bc47-20e8738785f2'}}
2025-08-12 15:02:06,543 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:07,079 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '86d47bfc-d558-4a4b-a44f-6f5aec5f8e8d'}}
2025-08-12 15:02:07,080 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:07,124 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0f2956f0-314e-490a-98df-f9d0528f6ec6'}}
2025-08-12 15:02:07,125 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:07,153 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:07,154 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:07,670 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '17455ed2-d509-4b0d-a124-50e0684ffeec'}}
2025-08-12 15:02:07,671 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:02:08,077 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'db513b96-c6a1-49a9-b51b-f6913c3972de'}}
2025-08-12 15:02:08,078 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:08,079 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1cb2c57c-3c81-4369-ae2d-d190b42b77df'}}
2025-08-12 15:02:08,080 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:08,559 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c3f44860-5185-41a4-a16c-4fe67dae3c0d'}}
2025-08-12 15:02:08,561 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:08,599 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cd136e83-4503-4477-8ae6-541ca87a2203'}}
2025-08-12 15:02:08,600 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:08,695 - INFO - 记录 714 无需翻译
2025-08-12 15:02:08,695 - INFO - 已处理 20 条记录，休息3秒...
2025-08-12 15:02:09,082 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:02:09,141 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '63d398dc-6edd-4008-9207-4bc0d054312e'}}
2025-08-12 15:02:09,141 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3e4f8ae3-0a83-4ab4-875a-19123d5f1b88'}}
2025-08-12 15:02:09,142 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:09,142 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:09,177 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0d55b724-7791-42ed-af8c-f9a16de4349f'}}
2025-08-12 15:02:09,178 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:09,210 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:09,211 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:10,142 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '2b6311c7-4910-40fa-a007-b865b38e1106'}}
2025-08-12 15:02:10,144 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:10,144 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:02:10,179 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:10,209 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '141020a2-2149-476c-8da5-663ad60954a2'}}
2025-08-12 15:02:10,211 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:10,211 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:02:10,246 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'eb7fa025-c4c8-4c75-aa9d-71e3b946ede4'}}
2025-08-12 15:02:10,247 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:10,282 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:10,285 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:10,614 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0e2b77c3-3a15-4904-a73c-cb972e2a32f9'}}
2025-08-12 15:02:10,615 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:10,651 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '02a23c01-8758-448e-8b0d-774a81747c90'}}
2025-08-12 15:02:10,652 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:11,146 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:02:11,201 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4d74c62c-d5a1-43dd-99a3-8fa89d1a5fea'}}
2025-08-12 15:02:11,202 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:11,208 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '695cbd68-dfd0-441a-94c8-381e7ae7bdab'}}
2025-08-12 15:02:11,209 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:11,317 - INFO - 翻译成功 - 字段: compositesecondary_chart_content, 原文长度: 548, 译文长度: 1903
2025-08-12 15:02:11,320 - INFO - 字段 compositesecondary_chart_content 翻译完成并通过验证
2025-08-12 15:02:11,617 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:02:11,657 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:02:11,678 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7a1be9eb-4d31-4d44-a0e9-18aaf20fcbd2'}}
2025-08-12 15:02:11,679 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:11,697 - INFO - 处理进度: 21/488
2025-08-12 15:02:11,697 - INFO - 开始翻译记录 ID: 713
2025-08-12 15:02:11,711 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:02:11,717 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8d94132c-d7b2-49f1-9db6-93640752c0c0'}}
2025-08-12 15:02:11,718 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:11,788 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '645c6222-1276-4bee-bf7a-885f2e495862'}}
2025-08-12 15:02:11,789 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:02:12,269 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7d13790f-bf5e-46e4-9cd8-4a718490500a'}}
2025-08-12 15:02:12,270 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:12,315 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '274d9c39-3b55-4998-9943-4724597e2c17'}}
2025-08-12 15:02:12,316 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:12,321 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:02:12,341 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:12,342 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:13,270 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '74a12293-0659-4b20-8fa2-e81bf1f2e8e7'}}
2025-08-12 15:02:13,270 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'f7813565-2f58-490f-8d51-b20d280f8967'}}
2025-08-12 15:02:13,271 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:13,271 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:13,737 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3f07c870-44a2-42fa-a95f-3459e11765f0'}}
2025-08-12 15:02:13,738 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:13,775 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6a0df0f3-efe2-4792-a02d-a0344111f2ee'}}
2025-08-12 15:02:13,776 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:13,853 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7594acaa-477a-441b-a0b3-d45fc81ec66e'}}
2025-08-12 15:02:13,854 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:02:14,271 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:02:14,327 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2738d586-a100-4bc8-99f5-dfc142bdb7f3'}}
2025-08-12 15:02:14,328 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '62bcef2e-5d7a-4bce-b2f7-a41582be6548'}}
2025-08-12 15:02:14,328 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:14,329 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:14,373 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '45cd3e77-0a93-490b-abba-152fa9fe3bf1'}}
2025-08-12 15:02:14,374 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:14,401 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:14,402 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:15,325 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '3780468a-00ba-460a-bdf2-f54942e983f1'}}
2025-08-12 15:02:15,326 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:15,334 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:02:15,378 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:02:15,402 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '725cc7e4-63f2-491f-8c32-2cb187ae03f7'}}
2025-08-12 15:02:15,403 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:15,403 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:02:15,430 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'deedeeae-6a94-4f59-9cd1-f26e09192d01'}}
2025-08-12 15:02:15,431 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:15,453 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:15,454 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:15,807 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '274e1dff-bea1-4b01-8416-228c94bcb254'}}
2025-08-12 15:02:15,808 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:15,834 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f518d785-ea16-478f-be98-ceacd01a6113'}}
2025-08-12 15:02:15,835 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:15,917 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c05cb9ab-5162-4061-83d1-496e4d6d8fa4'}}
2025-08-12 15:02:15,918 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:02:16,327 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:02:16,391 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '12d1fe10-f4e0-4604-8afd-cf4bce75614e'}}
2025-08-12 15:02:16,392 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:16,395 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7cbf43ca-2b78-4927-b651-a6be88321cb7'}}
2025-08-12 15:02:16,396 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:16,809 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:02:16,840 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:02:16,863 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'af6e65b8-cf92-4591-887b-a5a59151bb3d'}}
2025-08-12 15:02:16,865 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:16,900 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c984c8ce-d5af-415b-9e4b-0a6c5ee034be'}}
2025-08-12 15:02:16,901 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:16,923 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:02:16,981 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6fda8c73-cdaa-49ca-858c-77e687729aea'}}
2025-08-12 15:02:16,984 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:02:17,473 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cd75169e-edfc-4ec4-b108-4ca67c3d55e0'}}
2025-08-12 15:02:17,480 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:17,512 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '187e1ba1-f241-4d4c-a273-20918cf522f8'}}
2025-08-12 15:02:17,513 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:17,526 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:17,528 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:18,458 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1a195089-1130-466e-bfe9-5690c1e8d5f8'}}
2025-08-12 15:02:18,458 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '9ddd5ee2-f8ea-4f81-9f3e-017552e8dfaf'}}
2025-08-12 15:02:18,459 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:18,459 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:18,920 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '89cc2f45-0126-467a-bed1-25d60439596c'}}
2025-08-12 15:02:18,922 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:18,957 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c1dc3025-ad12-4eef-bb50-1c306611be18'}}
2025-08-12 15:02:18,959 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:19,049 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd183921e-efd3-44ee-90ea-ea38791dc6c4'}}
2025-08-12 15:02:19,050 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:02:19,464 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:02:19,514 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e0173cab-3bfe-4210-9748-1d367d5c4f12'}}
2025-08-12 15:02:19,515 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:19,530 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6a650f04-93d4-4ddb-8d5b-f2011f0a23f5'}}
2025-08-12 15:02:19,531 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:19,567 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c5ef557d-88bb-4175-a9af-4283979197d9'}}
2025-08-12 15:02:19,568 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:19,592 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:19,593 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:20,516 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '70bb080d-8729-4345-9b3f-7a04ce34f6b8'}}
2025-08-12 15:02:20,518 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:20,533 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:02:20,569 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:20,584 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '21a21d78-f22b-46b2-b262-056f45933369'}}
2025-08-12 15:02:20,586 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:20,595 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:02:20,618 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '05417f05-fba5-41ff-8b67-302260661972'}}
2025-08-12 15:02:20,619 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:20,649 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:20,650 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:20,978 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd69f969d-9137-4bfc-82e9-2ba94cfbde33'}}
2025-08-12 15:02:20,979 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:21,021 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8e192b06-5d9f-4253-931c-4a84226e32c4'}}
2025-08-12 15:02:21,024 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:21,113 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a7852988-4684-4581-998c-70f898b1d804'}}
2025-08-12 15:02:21,128 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:02:21,519 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:02:21,576 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'ec8ecf8f-5f67-466f-b8c2-5883f7ccd761'}}
2025-08-12 15:02:21,577 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:21,577 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6873a123-3957-427a-91e2-0bf921cd7ba7'}}
2025-08-12 15:02:21,578 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:21,982 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:02:22,029 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:02:22,045 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4d0b08e3-1f44-4343-ab20-58824056a6d7'}}
2025-08-12 15:02:22,046 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:22,080 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '65873588-8e57-4a5a-985d-f229e1bef16d'}}
2025-08-12 15:02:22,081 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:02:22,130 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:02:22,186 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ee88610f-9ba2-4c32-8b7e-5ab45b80b2a8'}}
2025-08-12 15:02:22,187 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:22,647 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '74b4e1a7-8ee1-49b5-88c4-9d3524c6cdc3'}}
2025-08-12 15:02:22,648 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:22,678 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd4991a2c-b20a-459f-9b4f-a1b39e8f607c'}}
2025-08-12 15:02:22,679 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:22,703 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:22,704 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:23,651 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '440e40b8-df5c-4870-bc57-d9615aee79b6'}}
2025-08-12 15:02:23,652 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '13ae4c35-b168-4f25-8247-256bd978a7cb'}}
2025-08-12 15:02:23,654 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:23,655 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:24,103 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5c1f978d-323e-46f2-8bfb-6d1f4ffb8b12'}}
2025-08-12 15:02:24,104 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:24,142 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c21575f7-c36f-4746-a108-28b422ed98a4'}}
2025-08-12 15:02:24,142 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:02:24,245 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '670c2189-3e60-4b47-8b3c-efebe3ae009c'}}
2025-08-12 15:02:24,246 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:24,659 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:24,710 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ad843696-5788-4be2-b7c3-575d6af80ade'}}
2025-08-12 15:02:24,711 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:24,718 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '062e82b4-388a-4547-8bc0-4207893f1a34'}}
2025-08-12 15:02:24,719 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:24,735 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b0f52434-dee1-440f-954e-f1ebb8ecca3c'}}
2025-08-12 15:02:24,736 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:24,760 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:24,761 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:25,576 - INFO - 翻译成功 - 字段: marks_a_chart_content, 原文长度: 274, 译文长度: 897
2025-08-12 15:02:25,579 - INFO - 字段 marks_a_chart_content 翻译完成并通过验证
2025-08-12 15:02:25,712 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:25,714 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'e7ac4317-109f-440f-9248-3ec54e1d3d9e'}}
2025-08-12 15:02:25,715 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:25,738 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:02:25,762 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:02:25,765 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '74c7825c-4a88-4624-ba04-02c7e1a3f9e1'}}
2025-08-12 15:02:25,766 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:25,792 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fdbd18ea-3078-48c8-aba7-d8875b086046'}}
2025-08-12 15:02:25,793 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:25,816 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:25,817 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:26,170 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4df5e206-9f9d-41c4-bb53-f2bd1cdb44d7'}}
2025-08-12 15:02:26,171 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:26,205 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3534cfe4-2dab-4949-a54d-4a2d30b735fa'}}
2025-08-12 15:02:26,208 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:02:26,316 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e0df161b-f5a1-487d-9ba4-d1f8a3ba6154'}}
2025-08-12 15:02:26,319 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:26,579 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:02:26,717 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:02:26,772 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'a7d48011-1ac8-4da2-a218-307d48e2a78c'}}
2025-08-12 15:02:26,773 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9321dab0-ead4-4e9a-92c0-8a3db4ed6389'}}
2025-08-12 15:02:26,773 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:02:26,774 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:27,176 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:02:27,211 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:02:27,234 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f95ac244-0568-48a1-8fc4-a9a245ca922e'}}
2025-08-12 15:02:27,235 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:27,275 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ea2b36cb-428c-445f-9ecf-398f3c376e9a'}}
2025-08-12 15:02:27,276 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:27,321 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:02:27,374 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7f44683b-cba2-4a1c-9b97-aac41d48835d'}}
2025-08-12 15:02:27,376 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:27,826 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e0e692d7-1750-4968-bec0-c468b449cf19'}}
2025-08-12 15:02:27,827 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:27,847 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6f57735e-2919-4111-9255-5fecff4b2f4b'}}
2025-08-12 15:02:27,848 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:27,870 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:27,871 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:28,833 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '105657d4-fac5-45c7-ba69-42217f8bfdd4'}}
2025-08-12 15:02:28,834 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:02:28,839 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c6aa7ef1-51a7-4123-aa71-6563c3845be9'}}
2025-08-12 15:02:28,840 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:29,288 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd05c4929-862b-40c8-81b0-8a27e0e66634'}}
2025-08-12 15:02:29,289 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:29,334 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '13cef90a-020b-42f8-a5c4-a6edf4a824ea'}}
2025-08-12 15:02:29,335 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:29,428 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f9709733-aacf-4e8e-88cb-2c4e2f6864b7'}}
2025-08-12 15:02:29,429 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:29,844 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:02:29,887 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '25001f57-5485-47bd-aacf-c54e8fb3a5a4'}}
2025-08-12 15:02:29,889 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:29,898 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3afbe87c-ca36-43fa-b276-e15ea83ab4ab'}}
2025-08-12 15:02:29,899 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:29,913 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ad543566-5f10-4bed-886f-48ed520247cc'}}
2025-08-12 15:02:29,914 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:29,929 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:29,931 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:30,890 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:02:30,917 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:02:30,924 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '1ff28408-499c-4568-9f57-46191f9c9420'}}
2025-08-12 15:02:30,925 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:02:30,935 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:02:30,943 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5754b497-277d-4e6d-877c-cd6281532c52'}}
2025-08-12 15:02:30,944 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:30,969 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd5fd09f3-92b0-4f95-b79b-c449839037c2'}}
2025-08-12 15:02:30,970 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:30,987 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:30,989 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:31,346 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a140afff-14ba-4fc3-8e1a-b0bea0ef1ea2'}}
2025-08-12 15:02:31,347 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:31,390 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '68f81d54-b5c3-43fa-a505-eeb009693716'}}
2025-08-12 15:02:31,391 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:31,485 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '380c3759-fe35-441d-8b5a-4c8ddce71131'}}
2025-08-12 15:02:31,486 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:31,926 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:02:31,957 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b73d6a7f-cf14-4d2c-b5cd-61256c7fe054'}}
2025-08-12 15:02:31,958 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:31,981 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '840ac7a7-eaef-4f3b-b6b5-8824f7cef832'}}
2025-08-12 15:02:31,983 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:32,351 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:02:32,392 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:02:32,412 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c598e270-e4e1-4df9-ab99-59e9245b8290'}}
2025-08-12 15:02:32,413 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:32,446 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a5388cea-70be-4d7c-ba04-0b613e828c4b'}}
2025-08-12 15:02:32,447 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:32,487 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:02:32,547 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7647089e-1013-47fb-91d5-ccb1d801f1a3'}}
2025-08-12 15:02:32,548 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:33,001 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '71b0a741-541d-4463-8419-687a0e6a91b8'}}
2025-08-12 15:02:33,002 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:33,019 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dc4f5b8e-e3dd-4831-b3a8-815047f55b47'}}
2025-08-12 15:02:33,019 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:33,042 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:33,042 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:34,016 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '287bee02-e55a-416b-92df-643d2a7eb029'}}
2025-08-12 15:02:34,017 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:34,034 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '3421f250-f915-48b7-a67b-d657ef70d0f0'}}
2025-08-12 15:02:34,035 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:34,470 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0ee65b47-5e9d-4666-aac7-9895270cfe6b'}}
2025-08-12 15:02:34,471 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:34,502 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b50ace34-207f-42cb-8d05-6c383fdc9fbf'}}
2025-08-12 15:02:34,503 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:34,602 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5d57718b-b6be-49a7-848d-efc582e900b3'}}
2025-08-12 15:02:34,603 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:35,018 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:35,054 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cb583de4-8638-475d-857d-17e50da49683'}}
2025-08-12 15:02:35,055 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:35,067 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4c09a93e-e382-4b3b-9bba-31d35422b405'}}
2025-08-12 15:02:35,068 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:35,079 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c374b2ea-ad55-4f4a-960e-8a7fb743edfb'}}
2025-08-12 15:02:35,080 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:35,098 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:35,099 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:36,056 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:36,081 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:02:36,086 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '95b5b45e-9de3-4d6c-8379-bba9334cbe20'}}
2025-08-12 15:02:36,087 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:36,100 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:02:36,105 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '959651bd-93b5-4d08-8a2a-18898aec8484'}}
2025-08-12 15:02:36,107 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:36,138 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8ca802a0-98be-49a3-a40f-0764c473a572'}}
2025-08-12 15:02:36,139 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:36,160 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:36,161 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:36,533 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2d450ef3-72d4-47fd-b579-2a83725b9b78'}}
2025-08-12 15:02:36,534 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:36,558 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6824a646-e2d4-4540-a23d-da4a268f4447'}}
2025-08-12 15:02:36,559 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:36,664 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ec0f9c83-a861-4829-aa03-54cb25832f4f'}}
2025-08-12 15:02:36,665 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:37,092 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:02:37,125 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '17dbab23-4915-4180-ae29-8c29181fa500'}}
2025-08-12 15:02:37,126 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:37,155 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'b0cc2300-8ece-47cd-a340-3cfbb8f5c54f'}}
2025-08-12 15:02:37,156 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:37,534 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:02:37,563 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:02:37,588 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd8ee653f-7449-4f46-905d-4a7cc130d955'}}
2025-08-12 15:02:37,589 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:37,611 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dddf07e0-e6f6-4a3b-8072-9c6b0c057fbc'}}
2025-08-12 15:02:37,612 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:37,667 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:02:37,723 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '80f8ab47-ae17-4dce-9f65-021672c5462d'}}
2025-08-12 15:02:37,724 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:38,161 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3cd6527f-c53b-453d-af31-b42e9ce5145b'}}
2025-08-12 15:02:38,162 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:38,189 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7a67f537-b736-4813-9712-8705dcab572f'}}
2025-08-12 15:02:38,190 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:38,210 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:38,211 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:39,175 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1ce4e37b-c36d-467f-a0ea-0a68de99695d'}}
2025-08-12 15:02:39,176 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:39,214 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '0c2c2bd3-e52f-4c8d-83f1-23305e4f74bb'}}
2025-08-12 15:02:39,215 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:39,660 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cea01a07-f0de-441b-824c-491872885ad7'}}
2025-08-12 15:02:39,661 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:39,677 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3cd16cf0-62f4-4a04-be61-f40ace902284'}}
2025-08-12 15:02:39,678 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:39,782 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ab5cf7a4-d10c-4646-9dd6-9b569e0b436c'}}
2025-08-12 15:02:39,783 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:40,178 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:02:40,234 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a5c5fb32-8a3d-42da-9aa9-fa874763f6df'}}
2025-08-12 15:02:40,235 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:40,239 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3692f848-24fe-4198-b454-654c0171952f'}}
2025-08-12 15:02:40,241 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:40,243 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a886a5e6-7249-4a4b-afa2-068bfb991ffc'}}
2025-08-12 15:02:40,244 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:40,262 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:40,263 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:41,187 - INFO - 翻译成功 - 字段: marks_b_chart_content, 原文长度: 307, 译文长度: 1054
2025-08-12 15:02:41,190 - INFO - 字段 marks_b_chart_content 翻译完成并通过验证
2025-08-12 15:02:41,240 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:02:41,247 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:02:41,268 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:02:41,274 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '54091ee2-4122-44aa-9d0a-48480aae2efa'}}
2025-08-12 15:02:41,275 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:41,295 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4b43f5bb-61c2-4b50-9dc1-d033e8178bbe'}}
2025-08-12 15:02:41,296 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:41,298 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '45b9e29b-6e0d-452a-bfd7-71a69e8ec768'}}
2025-08-12 15:02:41,299 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:41,324 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:41,325 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:41,717 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'accc4521-9a9b-4a91-ac5b-7d3c081d64a5'}}
2025-08-12 15:02:41,718 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:41,731 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b9b2850a-5248-4d99-a2a0-0d3a24783fc5'}}
2025-08-12 15:02:41,731 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:41,840 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b090f530-9848-456f-944d-9993d21c6b78'}}
2025-08-12 15:02:41,841 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:42,192 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:42,275 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:02:42,296 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cc1a613b-1f42-4e0a-91fc-c688b1e865a0'}}
2025-08-12 15:02:42,297 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:42,330 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '2dab351a-6ac0-4152-bff8-1df972794071'}}
2025-08-12 15:02:42,331 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:42,720 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:02:42,736 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:02:42,777 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a11fa22b-f38e-4f5e-8b80-c73e7772d0c7'}}
2025-08-12 15:02:42,778 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:42,787 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '02f6a92f-7c44-4bb2-97bf-d613e065bf2b'}}
2025-08-12 15:02:42,788 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:42,842 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:02:42,891 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7d18e449-5137-4578-9d0a-48265745d2e3'}}
2025-08-12 15:02:42,892 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:43,348 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ff6d0ed3-123d-4bc1-bf05-426b3ea8008c'}}
2025-08-12 15:02:43,349 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:43,355 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6ec41518-1b61-4206-93ed-a953467d7f0a'}}
2025-08-12 15:02:43,356 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:43,376 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:43,377 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:44,362 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6d25be7a-0480-4e5a-be5a-fd536739f0a7'}}
2025-08-12 15:02:44,363 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:44,389 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '544dacd4-1a58-4444-8536-2a536e835f7e'}}
2025-08-12 15:02:44,389 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:44,832 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2c21dd6a-3b26-4277-9dc1-37e9ce05fadf'}}
2025-08-12 15:02:44,833 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:44,846 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e38d0201-81b6-4bca-aeff-7bc56b1a2af0'}}
2025-08-12 15:02:44,847 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:44,945 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1a8ef428-798c-4118-8cf5-1646bdab3ebf'}}
2025-08-12 15:02:44,947 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:45,365 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:02:45,402 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e4e4beed-702b-4c74-8daa-1fd45874a5d6'}}
2025-08-12 15:02:45,403 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:45,411 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c96c4f3b-a35a-479c-a5b7-053335ddad7a'}}
2025-08-12 15:02:45,412 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:45,414 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dd9c0fad-41f0-4ea9-b4a4-9915dd2c0284'}}
2025-08-12 15:02:45,416 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:45,432 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:45,433 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:46,406 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:02:46,417 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:02:46,434 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:02:46,449 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '8c585958-fb39-451f-b56c-b4f4eb33c1dc'}}
2025-08-12 15:02:46,450 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:46,455 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '01dedc7c-fb39-4d49-8a6a-44bf83cbc211'}}
2025-08-12 15:02:46,456 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:02:46,470 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '991b48d2-b73f-47db-8a94-8e01985bd51e'}}
2025-08-12 15:02:46,471 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:46,484 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:46,486 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:46,888 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a73107f0-6956-4697-94a3-7348bc23943b'}}
2025-08-12 15:02:46,889 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:46,907 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ec4a200d-df38-4ea5-b160-9fe9618aff70'}}
2025-08-12 15:02:46,908 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:47,001 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '31775d0b-bb33-4daf-9b9d-358a192d56e0'}}
2025-08-12 15:02:47,002 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:47,451 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:02:47,472 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0f49fbb1-09ea-4498-a1d7-c8f2c2dec807'}}
2025-08-12 15:02:47,473 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:47,505 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'eeb56ee7-ebe9-4986-94a2-1b0eeff84e41'}}
2025-08-12 15:02:47,506 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:47,891 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:02:47,910 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:02:47,944 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9bdf2095-7578-4eda-8fd8-c4ddc8beadf0'}}
2025-08-12 15:02:47,945 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:47,971 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3fdf1d8b-6737-402f-8c07-821fbd553047'}}
2025-08-12 15:02:47,972 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:48,005 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:02:48,056 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'be4d75e0-096e-4010-b5b7-f1aa8e889649'}}
2025-08-12 15:02:48,057 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:48,519 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd1825c6b-fb2f-4337-b3be-13b4b1d751f6'}}
2025-08-12 15:02:48,520 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:02:48,528 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e661de51-92b6-4770-8970-bc2ff00626d3'}}
2025-08-12 15:02:48,529 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:48,541 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:48,542 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:49,524 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '00d071e7-54fa-46f4-99ed-93c86e711d40'}}
2025-08-12 15:02:49,525 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:49,568 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '59c37914-d912-4c71-ba13-69888d348bb5'}}
2025-08-12 15:02:49,570 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:50,001 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4693b3de-a288-4f03-bd06-93ffe9a38c9a'}}
2025-08-12 15:02:50,002 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:50,030 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6274f3c5-4c42-4e7d-9a96-942f69cb7623'}}
2025-08-12 15:02:50,031 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:50,113 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '84ac072a-ef49-4f0d-a334-78cca870314a'}}
2025-08-12 15:02:50,115 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:50,527 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:02:50,570 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '948077bd-ebcd-483b-b531-753da1b64ac1'}}
2025-08-12 15:02:50,571 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:02:50,579 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '656cad28-30bf-4b47-b7c7-ccc8628c9799'}}
2025-08-12 15:02:50,580 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:50,583 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0c2192eb-68b5-4842-850e-72962f6f1912'}}
2025-08-12 15:02:50,584 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:50,589 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:50,590 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:51,587 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:02:51,590 - INFO - 记录 718 无需翻译
2025-08-12 15:02:51,590 - INFO - 处理进度: 17/488
2025-08-12 15:02:51,590 - INFO - 开始翻译记录 ID: 717
2025-08-12 15:02:51,590 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:02:51,607 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:02:51,626 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '3232f90b-33f4-485f-9c35-fabb8d56fa0b'}}
2025-08-12 15:02:51,627 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:51,639 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3eba856e-7f15-4baf-a2fe-519092d17157'}}
2025-08-12 15:02:51,640 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:51,641 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:51,642 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:51,656 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '57af80f3-f8f6-4902-b1d9-25e2d9ae10d1'}}
2025-08-12 15:02:51,657 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:02:52,064 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '51a3ca28-5d28-409f-8c5f-7603711fbd0e'}}
2025-08-12 15:02:52,066 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:52,086 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8d135337-335a-4f91-bf7e-c9635c05ea48'}}
2025-08-12 15:02:52,088 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:52,166 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fdd71318-bd2a-4c98-ab8d-0a25249fca11'}}
2025-08-12 15:02:52,167 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:52,632 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:02:52,634 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '04c2b808-28b7-4477-b2ef-6cbb8ee751c1'}}
2025-08-12 15:02:52,635 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:52,681 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '08739609-d63c-4f29-a278-a872d6cc0c34'}}
2025-08-12 15:02:52,682 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:53,072 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:02:53,093 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:02:53,133 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '50e6557b-bc30-4184-8ff7-d4d85ab12825'}}
2025-08-12 15:02:53,136 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:02:53,155 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a46b958c-c177-44f8-9b54-4a96fbbaa622'}}
2025-08-12 15:02:53,157 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:53,172 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:02:53,232 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0017fceb-4449-4706-924c-3b81485c3694'}}
2025-08-12 15:02:53,236 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:53,698 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:53,698 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a513ddce-7d61-4213-9ffb-60c0dc7ef9b3'}}
2025-08-12 15:02:53,699 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:53,699 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:53,712 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c1141889-a046-4631-87e6-5065db5c978c'}}
2025-08-12 15:02:53,713 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:02:54,697 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '197d34d7-3725-4547-979e-2b2e3bb226a7'}}
2025-08-12 15:02:54,697 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:54,744 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '60a32660-573d-4bd4-9509-08048bd20383'}}
2025-08-12 15:02:54,745 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:55,186 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c1ca1772-960a-43b3-a393-deca0f39ee8e'}}
2025-08-12 15:02:55,187 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:02:55,217 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b7a9f30c-7a2d-471c-baac-5b8770505e5e'}}
2025-08-12 15:02:55,218 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:55,285 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4e3065ee-9277-4c7c-b5de-7e8e106994f3'}}
2025-08-12 15:02:55,286 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:55,699 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:02:55,754 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:55,755 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:55,756 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ae18c153-75c3-4e0f-8c1a-f941c90f9f38'}}
2025-08-12 15:02:55,757 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2e1b07b6-b1de-48fe-8007-f58b0de60e2a'}}
2025-08-12 15:02:55,757 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:55,758 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:55,767 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c9772c76-2ecf-4203-9656-8e033c2c731a'}}
2025-08-12 15:02:55,768 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:02:56,758 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:02:56,760 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:02:56,771 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:02:56,803 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '10f6cd8c-78c0-4d9a-b3ce-e52ce211fc2e'}}
2025-08-12 15:02:56,804 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:56,811 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:56,812 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:02:56,816 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fc499fd3-3f19-4e2e-abaa-26a3a8792ea5'}}
2025-08-12 15:02:56,817 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:56,824 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4c64c7c3-e6d6-4c04-99a3-ec6d553dc05e'}}
2025-08-12 15:02:56,826 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:02:57,246 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '17a8dc43-cc7a-44e0-95ed-83aace3b9b10'}}
2025-08-12 15:02:57,246 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:02:57,275 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'aabf78f0-520f-4523-92c0-41f65c5c1e63'}}
2025-08-12 15:02:57,276 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:57,352 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'edf28436-de8c-4fb1-b448-d75e9f41f761'}}
2025-08-12 15:02:57,353 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:57,809 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:02:57,825 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '596e63ab-aa9c-40fd-95c9-1ebad224df4f'}}
2025-08-12 15:02:57,826 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:57,906 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'a161ab81-4a08-4255-ba57-6027821cca51'}}
2025-08-12 15:02:57,907 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:58,249 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:02:58,277 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:02:58,355 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:02:58,409 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0b86a7f4-bcd0-41ec-b24f-6f044990e3ca'}}
2025-08-12 15:02:58,410 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:58,421 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a961cab7-97e7-4041-803c-621639f3343e'}}
2025-08-12 15:02:58,422 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:58,469 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b94e2ef0-a8b2-42b7-9441-f94eaf5b65e0'}}
2025-08-12 15:02:58,470 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:02:58,954 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:02:58,956 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:02:58,972 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '95a8aa4b-92e1-4c4c-aa84-780d2511b2d7'}}
2025-08-12 15:02:58,973 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:02:59,016 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9b783ec5-68ee-4f07-8c26-1b10203237d0'}}
2025-08-12 15:02:59,019 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:02:59,895 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9af058ce-5933-42ad-a251-7a27c3de5143'}}
2025-08-12 15:02:59,896 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:02:59,964 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '31881867-9009-4183-9ab9-0524ae7a8fa6'}}
2025-08-12 15:02:59,965 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:00,569 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '64473d6d-8744-4cc7-9371-7e53ab60f369'}}
2025-08-12 15:03:00,570 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:00,597 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7ed5f46d-48ee-4b92-ac9e-82d7484e331f'}}
2025-08-12 15:03:00,598 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:00,629 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2400bfb3-ca61-43e9-831b-70e18dbdfa1d'}}
2025-08-12 15:03:00,630 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:00,902 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:03:00,956 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '01f3c725-94c8-4d7e-988f-e3f93dc31142'}}
2025-08-12 15:03:00,958 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:03:01,018 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:01,019 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:03:01,036 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4e42ede3-673d-4423-9dcc-1aa09477df67'}}
2025-08-12 15:03:01,037 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:03:01,070 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5f820df4-bdac-491a-870b-3327cb4f19d0'}}
2025-08-12 15:03:01,071 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:02,022 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:03:02,037 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:03:02,045 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '3f71c3e2-0720-4d94-ad7b-6702971ff3fb'}}
2025-08-12 15:03:02,046 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:02,074 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:03:02,109 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:02,110 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:02,137 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ebff9853-156b-4967-adbc-92990c30acae'}}
2025-08-12 15:03:02,138 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:02,156 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '903e7925-d597-406f-9241-b360cb6c1817'}}
2025-08-12 15:03:02,157 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:03:02,266 - INFO - 翻译成功 - 字段: marksthirprogr_a_chart_content, 原文长度: 514, 译文长度: 1648
2025-08-12 15:03:02,268 - INFO - 字段 marksthirprogr_a_chart_content 翻译完成并通过验证
2025-08-12 15:03:02,683 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e0915258-b30c-4037-af3b-2cb41d90e417'}}
2025-08-12 15:03:02,684 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:02,685 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f90848cf-2d93-4bed-a745-28e21e0ed148'}}
2025-08-12 15:03:02,686 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:02,718 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f27ab20e-d260-4359-ad67-b10b50aa1333'}}
2025-08-12 15:03:02,719 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:03,047 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:03,055 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f89a230b-6513-4242-a4cf-8ff12ef41d28'}}
2025-08-12 15:03:03,057 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:03:03,140 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '84857bba-b534-4f10-9719-1b980626c54c'}}
2025-08-12 15:03:03,142 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:03,274 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:03,687 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:03,691 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:03:03,722 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:03:03,770 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ca2eab4a-d2ee-4c38-bfc1-a836db3d0454'}}
2025-08-12 15:03:03,770 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4232bd1d-2bea-4869-9513-cb3c0667cdda'}}
2025-08-12 15:03:03,771 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:03,771 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:03,820 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a8258851-8b9c-4229-ac7e-6e6036a2bb25'}}
2025-08-12 15:03:03,822 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:04,208 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:04,209 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:04,232 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '272a5933-4d8e-463c-bdab-08388429d66f'}}
2025-08-12 15:03:04,233 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:04,239 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '585bbf1f-f907-4e2d-950f-972df21c8ab8'}}
2025-08-12 15:03:04,240 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:03:05,150 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c1ef0311-4f6d-46af-b7b1-12971c104c3a'}}
2025-08-12 15:03:05,153 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:03:05,241 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'b2660082-2f61-477d-b4e5-88e0405cacff'}}
2025-08-12 15:03:05,242 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:05,869 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3ad0962f-a213-47fd-abac-01f4d44f9ed8'}}
2025-08-12 15:03:05,870 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:05,872 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5e323942-ca32-47f4-8d9b-3a52d8662d1f'}}
2025-08-12 15:03:05,873 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:05,876 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c2c760da-aad4-4a3f-a432-6a46b773685a'}}
2025-08-12 15:03:05,877 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:06,184 - INFO - 记录 714 无需翻译
2025-08-12 15:03:06,184 - INFO - 已处理 20 条记录，休息3秒...
2025-08-12 15:03:06,269 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:06,270 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:06,329 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dfa13604-2427-4198-a958-121ba58bef60'}}
2025-08-12 15:03:06,330 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:06,333 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6a1c0567-4dae-412d-aae4-91e1b7e2695c'}}
2025-08-12 15:03:06,334 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:03:07,270 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:03:07,331 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:03:07,413 - INFO - 记录 718 无需翻译
2025-08-12 15:03:07,413 - INFO - 处理进度: 17/488
2025-08-12 15:03:07,413 - INFO - 开始翻译记录 ID: 717
2025-08-12 15:03:07,465 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:07,468 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:07,510 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '4c61f028-d417-4f82-803c-d63020058d23'}}
2025-08-12 15:03:07,511 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:07,519 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:03:07,530 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c2b79d48-d935-46c9-8b1b-4dfdbbf91585'}}
2025-08-12 15:03:07,531 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:07,660 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cf96e7f1-b839-4cc6-bc62-0e9f98a1a69a'}}
2025-08-12 15:03:07,661 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:03:07,947 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'aea9a89f-710b-412a-9ca9-4cd9064ca66f'}}
2025-08-12 15:03:07,947 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3ba98505-1d81-4190-b204-15e30a06a53f'}}
2025-08-12 15:03:07,948 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:07,948 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:07,949 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '841b9783-bd34-46bb-909b-7f60c14f2863'}}
2025-08-12 15:03:07,950 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:08,512 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:08,618 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '145bf591-0cc9-455b-b67c-023dccfb2bd5'}}
2025-08-12 15:03:08,621 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:08,949 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:03:08,954 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:03:08,954 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:09,053 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '885f0b42-3198-42d7-bf6d-5ed9d7e5bc9c'}}
2025-08-12 15:03:09,053 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '39c1fa84-cddc-4333-9cb0-08e6f56db650'}}
2025-08-12 15:03:09,054 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:09,054 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:09,058 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2f0eb03e-b44d-4335-a832-f06d6a7c9e98'}}
2025-08-12 15:03:09,061 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:09,186 - INFO - 处理进度: 21/488
2025-08-12 15:03:09,186 - INFO - 开始翻译记录 ID: 713
2025-08-12 15:03:09,228 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:03:09,293 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9046973c-89b4-4008-9c9b-4a1cd3003173'}}
2025-08-12 15:03:09,294 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:03:09,522 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:09,523 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:09,588 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'caa3f4c5-43b6-4868-9adc-cc5345b67567'}}
2025-08-12 15:03:09,591 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:09,726 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c55cd948-6b15-4108-b910-367f3532b7af'}}
2025-08-12 15:03:09,730 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:03:10,675 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '8aee150b-8e1b-44e1-a7c8-7d67f36517ec'}}
2025-08-12 15:03:10,676 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:11,121 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c87dd404-a1c1-43cb-8bb2-6af73a63585c'}}
2025-08-12 15:03:11,122 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1d638e7b-4859-4b1a-98a0-3c139d5c3f49'}}
2025-08-12 15:03:11,123 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e2e3f06c-d34a-41ed-87df-2b98ab737573'}}
2025-08-12 15:03:11,123 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:11,124 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:11,125 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:11,357 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ee5b4b5a-67bf-44a2-b405-908c84eb395d'}}
2025-08-12 15:03:11,359 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:03:11,575 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:11,576 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:11,642 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '107b173d-86d0-4f75-893c-9af74ebdf503'}}
2025-08-12 15:03:11,643 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:11,807 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'da87815a-33a1-4f81-846e-d76024ddc2c9'}}
2025-08-12 15:03:11,811 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:03:12,581 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:03:12,638 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:12,640 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:12,645 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:03:12,696 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b5de4061-14b1-4e98-9b2f-9156a7b858c7'}}
2025-08-12 15:03:12,697 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:12,733 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '1818b052-5071-4ca4-954d-ed04e3a70dfa'}}
2025-08-12 15:03:12,734 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:12,813 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:03:12,869 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f82b127e-6919-4600-86ee-ca4141e0abac'}}
2025-08-12 15:03:12,870 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:03:13,181 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '02fe5fd5-05cd-4ee3-862d-e06bf7669023'}}
2025-08-12 15:03:13,183 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:13,185 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f9c0f668-ee80-41d8-970b-d2ac99f6cd84'}}
2025-08-12 15:03:13,186 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:13,187 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a345ad85-8fb0-4f7f-87df-0b33391d244e'}}
2025-08-12 15:03:13,188 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:13,420 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cf971caf-d809-4f85-b12e-b57dff66b0c7'}}
2025-08-12 15:03:13,421 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:03:13,738 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:13,793 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '90b47bd8-27f8-4562-9963-cadf2b472aa6'}}
2025-08-12 15:03:13,794 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:14,187 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:03:14,187 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:14,188 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:03:14,246 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a7f028ab-64c6-4347-94e8-4d51832a67af'}}
2025-08-12 15:03:14,246 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '08eac7dc-8f49-485b-bf88-148d16073c2e'}}
2025-08-12 15:03:14,247 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:14,247 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:14,250 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6f1cfa93-50e2-42b3-afb3-95eb9e42110f'}}
2025-08-12 15:03:14,251 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:14,422 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:03:14,476 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '76bea8bc-dfe6-4dce-8450-60cf7ac74077'}}
2025-08-12 15:03:14,477 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:03:14,696 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:14,699 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:14,756 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '411d5475-80c4-4ff2-b868-67e17d1d4162'}}
2025-08-12 15:03:14,759 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:14,928 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '837c81d2-40b9-4f26-90d5-126e2c238304'}}
2025-08-12 15:03:14,929 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:03:15,853 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'd18d320b-9a0d-440a-8895-eb47e8ddd463'}}
2025-08-12 15:03:15,854 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:16,315 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c9f7ecfb-0323-49e6-bce8-1cbe4561c1e9'}}
2025-08-12 15:03:16,315 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b90674f0-cc8b-4830-8940-5c1d0f767ff6'}}
2025-08-12 15:03:16,316 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '92ae8123-0931-403a-800a-841a94abb8d4'}}
2025-08-12 15:03:16,317 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:16,317 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:16,319 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:16,542 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '36e15b42-19ba-4d5e-bc6e-0b64e7dd1fd8'}}
2025-08-12 15:03:16,545 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:03:16,764 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:16,766 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:16,820 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ea28afe2-a435-4a3f-b169-1a60f62cae3a'}}
2025-08-12 15:03:16,823 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:17,001 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ec6cb38e-b196-4204-9856-eb35fcf3c489'}}
2025-08-12 15:03:17,004 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:03:17,769 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:03:17,820 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:17,821 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:17,825 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:03:17,876 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '16635ff8-a4f9-4a25-8c28-4dc9abd18d9c'}}
2025-08-12 15:03:17,877 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:17,919 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '7bcb47c2-f562-46cb-9f04-25b824920e01'}}
2025-08-12 15:03:17,921 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:18,009 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:03:18,072 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5b4388b3-e782-4110-9c9b-b6cd3aefbf69'}}
2025-08-12 15:03:18,076 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:18,369 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b6f51623-9839-40c5-88b7-83f25bac3a25'}}
2025-08-12 15:03:18,370 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:18,382 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fd21b837-6e19-407f-b240-7819f0129bfb'}}
2025-08-12 15:03:18,383 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:18,384 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd840abb7-849f-46f5-b361-2a98ac56762b'}}
2025-08-12 15:03:18,385 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:18,604 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4d155d81-01b2-4599-a19a-5d02ddae7871'}}
2025-08-12 15:03:18,608 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:03:18,924 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:19,019 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'a7a31f3e-058b-411d-9111-6114ffd6c53c'}}
2025-08-12 15:03:19,021 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:19,372 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:03:19,385 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:03:19,385 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:03:19,426 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'eecdc9ca-377e-4387-8a6a-4b34e1088cd4'}}
2025-08-12 15:03:19,427 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:19,435 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd0aa6c5a-a256-4927-92d6-eedddc5e67ea'}}
2025-08-12 15:03:19,436 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:19,438 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7b42b149-5567-4002-9147-207e60e14c8d'}}
2025-08-12 15:03:19,439 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:19,611 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:03:19,667 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '37faa462-8f9e-4621-903a-05bb8617840e'}}
2025-08-12 15:03:19,668 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:19,882 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:19,883 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:19,924 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '94e991e0-080b-4bc6-81b4-051ad5252d08'}}
2025-08-12 15:03:19,925 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:20,135 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '799b48a1-e9a9-40a5-bd45-f1c04e4fcc85'}}
2025-08-12 15:03:20,138 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:21,077 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'deac4f8e-0397-4914-90f8-6c42bb8683a9'}}
2025-08-12 15:03:21,081 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:21,487 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '011fc00d-cafc-4b9e-9630-338eff46aefe'}}
2025-08-12 15:03:21,488 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:21,498 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a695aa69-352e-48f2-9e8d-63840f84326c'}}
2025-08-12 15:03:21,498 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '933cac26-958a-40b8-b4d1-c70e9ebf0ffe'}}
2025-08-12 15:03:21,500 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:21,500 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:21,727 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5cdb809f-71e7-46d0-8e9c-5feda7e111b0'}}
2025-08-12 15:03:21,728 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:21,940 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:21,941 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:21,976 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b649de5c-c526-4bf3-ba34-3260243009b4'}}
2025-08-12 15:03:21,977 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:22,199 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2a65aa66-c711-4bae-93f5-4b7d0f6dd1b2'}}
2025-08-12 15:03:22,202 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:22,944 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:03:22,982 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:03:22,997 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:22,998 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:23,033 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '16e83ce2-0951-41c1-8f80-c106ef0623d5'}}
2025-08-12 15:03:23,036 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:23,137 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'cfebb537-5341-4609-bcb1-d6750efe297e'}}
2025-08-12 15:03:23,138 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:23,207 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:03:23,271 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '42a63fec-b44c-4db3-b97d-eea292530dfb'}}
2025-08-12 15:03:23,273 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:23,549 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9885dadd-22c4-4d95-a09b-2a2e796f0203'}}
2025-08-12 15:03:23,551 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '56d69433-85b9-4f43-83eb-0bd1cf57d87b'}}
2025-08-12 15:03:23,552 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:23,552 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:23,554 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1b9c9ade-1975-4dde-a349-a9de0553b121'}}
2025-08-12 15:03:23,554 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:23,790 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cc377a6b-e6d3-4765-99f8-f18422ec7233'}}
2025-08-12 15:03:23,793 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:24,143 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:03:24,199 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '763d5df8-c456-4f5a-8c40-28ae91d5e7dd'}}
2025-08-12 15:03:24,200 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:24,552 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:03:24,555 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:03:24,555 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:03:24,604 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '19a84f29-eaf4-4294-a978-33f433c87899'}}
2025-08-12 15:03:24,605 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:24,607 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0c427a5b-9a18-4632-bd4f-2574ae884295'}}
2025-08-12 15:03:24,608 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:03:24,610 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e5381616-7eb7-4cbe-b246-ffa2745b10f5'}}
2025-08-12 15:03:24,611 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:24,795 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:03:24,854 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8079e47d-9213-4dc3-868a-c066c9b9cb92'}}
2025-08-12 15:03:24,855 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:25,051 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:25,052 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:25,089 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '475dbf17-c3b8-4023-b65e-6e2ede056fdf'}}
2025-08-12 15:03:25,092 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:25,327 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '798d2c8e-f012-4f44-bbc7-f40bc53b6a44'}}
2025-08-12 15:03:25,328 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:26,267 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'fab291ef-48fa-4bd5-874e-66c13432b296'}}
2025-08-12 15:03:26,270 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:26,652 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '45e5c2be-2154-4443-b0c8-90f062652431'}}
2025-08-12 15:03:26,653 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:26,660 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b53a3ba2-808c-4972-a19b-4bee55ed4cf1'}}
2025-08-12 15:03:26,660 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '46d79766-fe76-4cd8-b9bf-29470f5ceb5e'}}
2025-08-12 15:03:26,661 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:26,661 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:03:26,907 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '80a76ec9-cfc2-4bb3-863e-b4fa184cc525'}}
2025-08-12 15:03:26,908 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:27,101 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:27,102 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:27,146 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '332b81bf-c88f-4e4e-8582-dac9106ffcb6'}}
2025-08-12 15:03:27,147 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:27,386 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'de007e5c-cbd7-4f7e-9955-6ba94db67a7e'}}
2025-08-12 15:03:27,389 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:28,102 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:03:28,148 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:03:28,155 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:28,156 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:28,205 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '109443d1-f9d4-459f-b645-315beda759a5'}}
2025-08-12 15:03:28,206 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:28,332 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '067a160b-f205-4cf1-b126-7ba28dfb7cde'}}
2025-08-12 15:03:28,333 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:28,393 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:03:28,457 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '52fa4896-626b-45c8-89aa-035252762579'}}
2025-08-12 15:03:28,458 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:28,690 - INFO - 翻译成功 - 字段: marksthirprogr_b_chart_content, 原文长度: 626, 译文长度: 2190
2025-08-12 15:03:28,692 - INFO - 字段 marksthirprogr_b_chart_content 翻译完成并通过验证
2025-08-12 15:03:28,714 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '92d2ca7a-fdef-495f-9d1b-1ff91ca0bb89'}}
2025-08-12 15:03:28,714 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f1207b1a-f0a5-41cc-8252-627f8e995e48'}}
2025-08-12 15:03:28,716 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:28,716 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'db24f335-2d72-4561-91b4-173183ba931d'}}
2025-08-12 15:03:28,716 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:28,717 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:03:28,966 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '586b1ab5-0dff-41a6-aba0-2a0981b7dcaf'}}
2025-08-12 15:03:28,967 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:29,338 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:03:29,388 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'e65a1f77-311d-489e-a85d-18bff7c8dab6'}}
2025-08-12 15:03:29,389 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:29,695 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:29,716 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:03:29,717 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:03:29,721 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:29,768 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3c8e69a0-d3b2-4686-8c0d-01a3c91d1745'}}
2025-08-12 15:03:29,768 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'bd79c33c-d7df-44b3-bd87-8374a87769e7'}}
2025-08-12 15:03:29,769 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:29,769 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:29,773 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '33527c74-e257-452f-86e6-a62b0cf214b7'}}
2025-08-12 15:03:29,774 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:29,972 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:03:30,024 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '62e4e396-e30a-45c4-aa25-dcc748f99c61'}}
2025-08-12 15:03:30,025 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:30,222 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:30,223 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:30,272 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1b46be98-0ad2-4834-804f-e2717edde40a'}}
2025-08-12 15:03:30,276 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:30,518 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6b140005-0d3c-407e-bbb3-797d01a85279'}}
2025-08-12 15:03:30,519 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:31,452 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '071c94bf-c01e-46b3-9505-f62da875edb5'}}
2025-08-12 15:03:31,453 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:31,828 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '99d39d23-83b3-45df-ad89-601e459139e4'}}
2025-08-12 15:03:31,828 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c7c29e3c-6f1b-4e08-b2b2-413775687b19'}}
2025-08-12 15:03:31,830 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:31,830 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:31,831 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8bf305be-6201-44a4-addd-03027c1ffbaf'}}
2025-08-12 15:03:31,833 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:32,091 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'afa7c692-c3c8-420b-a771-e3c622f92f84'}}
2025-08-12 15:03:32,094 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:32,289 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:32,290 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:32,341 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dbc546fe-3815-441a-9db8-f0277090bd2a'}}
2025-08-12 15:03:32,344 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:32,569 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '077ee557-0d2d-4840-8fa3-4fcfa359ba14'}}
2025-08-12 15:03:32,570 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:33,291 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:33,347 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:33,348 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:33,349 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:03:33,385 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '307a8c67-2121-44b3-8050-71ee63d145ff'}}
2025-08-12 15:03:33,386 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:33,501 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '3ccfc6c8-8d28-4018-9358-3e007940f1a5'}}
2025-08-12 15:03:33,502 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:33,575 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:03:33,617 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a6f943b9-b525-49e4-a897-ada65a8ec7b0'}}
2025-08-12 15:03:33,618 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:33,877 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '41ef6752-06c7-4e28-b299-1b3006d29d54'}}
2025-08-12 15:03:33,877 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '04da3be4-86c9-4754-bc55-9bbd70c6706d'}}
2025-08-12 15:03:33,877 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '96de2402-3e73-454b-bbee-542433b12d84'}}
2025-08-12 15:03:33,878 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:33,878 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:33,878 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:34,132 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c9dbb376-e945-4364-b169-62ba11623e52'}}
2025-08-12 15:03:34,133 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:34,504 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:03:34,540 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'e519439d-82cf-439f-bd0e-c09f6137cfb7'}}
2025-08-12 15:03:34,541 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:34,879 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:03:34,882 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:34,883 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:03:34,924 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7ea9f1d7-89cb-4912-934c-ee5ed1190eab'}}
2025-08-12 15:03:34,924 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '01a6b4b5-aa7a-4274-b5a7-05dca5e2d128'}}
2025-08-12 15:03:34,925 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cf00865e-029a-4898-8d66-409e766bdb31'}}
2025-08-12 15:03:34,925 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:03:34,925 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:34,926 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:35,138 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:03:35,177 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '27c85f46-18e0-4a34-ad5d-b951c54055f7'}}
2025-08-12 15:03:35,179 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:35,398 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:35,399 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:35,425 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '30dad154-acc3-4c3d-a559-013abb144a2f'}}
2025-08-12 15:03:35,427 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:35,656 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9caebc64-ee4d-4745-8e7d-5010a86ece65'}}
2025-08-12 15:03:35,657 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:36,589 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'b29b7546-4c40-4c26-a1a8-bfd03bdbc3e7'}}
2025-08-12 15:03:36,590 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:36,971 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f8e636d9-d4e4-4f29-a82f-97da9794f535'}}
2025-08-12 15:03:36,972 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:36,973 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '360e07d7-4d07-4ba5-847f-bb09c3145aa2'}}
2025-08-12 15:03:36,974 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:03:36,980 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dc942504-2193-4cd5-af5e-599458e86ffd'}}
2025-08-12 15:03:36,982 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:37,223 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c4e81269-a0f9-4923-898e-23f4709ff7b4'}}
2025-08-12 15:03:37,224 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:37,446 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:37,447 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:37,465 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0f34d159-f8b1-47a2-a7f6-934e0aa60c0c'}}
2025-08-12 15:03:37,467 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:37,696 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1ced6368-c569-40fc-893b-c1d27cd5dcf8'}}
2025-08-12 15:03:37,697 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:38,452 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:38,469 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:03:38,529 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:38,530 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:38,538 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1a346495-3dfe-4aa7-8c69-187edc31c015'}}
2025-08-12 15:03:38,539 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:38,649 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'a1c508df-6588-48f9-abff-89c439dac4e9'}}
2025-08-12 15:03:38,652 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:38,703 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:03:38,747 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '42a38e40-60c7-432e-b767-0601da7eeb50'}}
2025-08-12 15:03:38,748 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:39,016 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6a938e5f-0792-49fa-b212-f929404cf764'}}
2025-08-12 15:03:39,017 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:03:39,019 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0b55785d-0225-42ec-baaf-3a5dbe196961'}}
2025-08-12 15:03:39,020 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:39,023 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4e7fe1c6-a327-44a8-9b65-03cce26252f0'}}
2025-08-12 15:03:39,024 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:39,260 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '359ff7a8-9604-4d74-a430-1f22d07543a3'}}
2025-08-12 15:03:39,262 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:39,655 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:03:39,707 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'd941f71a-6d5d-430e-90d7-216ccd0d5232'}}
2025-08-12 15:03:39,709 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:03:40,022 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:40,025 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:03:40,039 - INFO - 记录 713 无需翻译
2025-08-12 15:03:40,039 - INFO - 处理进度: 22/488
2025-08-12 15:03:40,039 - INFO - 开始翻译记录 ID: 712
2025-08-12 15:03:40,056 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:03:40,063 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8db26d83-5f27-4915-ba54-6efbea529dcb'}}
2025-08-12 15:03:40,063 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '33e71d10-bb23-4c36-9fd6-c21a9f5675bf'}}
2025-08-12 15:03:40,064 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:40,064 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:40,097 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'eb88ad12-29e6-4233-b3da-d96eb9786da3'}}
2025-08-12 15:03:40,098 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:03:40,265 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:03:40,317 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0a64b563-3b98-43d1-b3c2-9872c32367c6'}}
2025-08-12 15:03:40,318 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:40,571 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:40,572 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:40,578 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4be9b8c1-6af0-4d20-b164-1efcb7d836e4'}}
2025-08-12 15:03:40,579 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:40,797 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a53456db-1970-4605-81ed-d16af669a8af'}}
2025-08-12 15:03:40,798 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:41,753 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'f21e9055-f8b3-4d12-9630-2abb95563c8a'}}
2025-08-12 15:03:41,754 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:03:42,114 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '081f3493-0385-42ea-8cd7-cea7e1609bf1'}}
2025-08-12 15:03:42,116 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:42,117 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '369941a8-5911-47d6-a824-c57fb68b5456'}}
2025-08-12 15:03:42,120 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:42,155 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1f88e8ed-2151-425e-9b36-faaa66fe46a1'}}
2025-08-12 15:03:42,161 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:03:42,360 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7b806a3f-ad42-4129-a35e-8f0f955b0746'}}
2025-08-12 15:03:42,361 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:42,624 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:42,624 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1d95ca4d-dc99-4fa9-9fd5-a3bcd040f488'}}
2025-08-12 15:03:42,627 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:42,627 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:42,835 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b8c3d905-edbf-4b32-8b12-9a2864f47179'}}
2025-08-12 15:03:42,836 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:43,628 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:03:43,628 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:03:43,656 - INFO - 找到 488 条需要翻译的记录
2025-08-12 15:03:43,656 - INFO - 开始翻译 488 条记录
2025-08-12 15:03:43,656 - INFO - 错误日志将记录到: /Users/<USER>/Downloads/test/corpus_en/translation_errors.txt
2025-08-12 15:03:43,656 - INFO - 处理进度: 1/488
2025-08-12 15:03:43,656 - INFO - 开始翻译记录 ID: 733
2025-08-12 15:03:43,666 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:43,667 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:43,667 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '71a9649d-6213-4ec3-a18b-f5f85bb576bc'}}
2025-08-12 15:03:43,668 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:43,687 - INFO - 记录 733 无需翻译
2025-08-12 15:03:43,687 - INFO - 处理进度: 2/488
2025-08-12 15:03:43,687 - INFO - 开始翻译记录 ID: 732
2025-08-12 15:03:43,722 - INFO - 记录 732 无需翻译
2025-08-12 15:03:43,722 - INFO - 处理进度: 3/488
2025-08-12 15:03:43,722 - INFO - 开始翻译记录 ID: 731
2025-08-12 15:03:43,741 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:03:43,791 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'b268799e-c5f1-48ea-a7e4-701885aa8b29'}}
2025-08-12 15:03:43,792 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:03:43,841 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:03:43,875 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd75dae44-7388-4a75-b2ce-6b1f0e2f6499'}}
2025-08-12 15:03:43,876 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:44,040 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:44,041 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:44,164 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '445f349d-b75f-43d7-b227-81d1a118973d'}}
2025-08-12 15:03:44,164 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c7d15202-6f4d-4d8e-8d2e-d53f94f9c027'}}
2025-08-12 15:03:44,165 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:44,166 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:44,199 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '59b5b404-968b-4384-a481-0a079df065ce'}}
2025-08-12 15:03:44,200 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:03:44,399 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a256fd1e-1158-4f21-9686-88dc1c1b0322'}}
2025-08-12 15:03:44,401 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:44,807 - INFO - 记录 715 无需翻译
2025-08-12 15:03:44,807 - INFO - 处理进度: 20/488
2025-08-12 15:03:44,807 - INFO - 开始翻译记录 ID: 714
2025-08-12 15:03:44,823 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:03:44,860 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '1d726aa8-6233-4407-aa27-01d9bb6e63ed'}}
2025-08-12 15:03:44,861 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:03:45,171 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:45,171 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:03:45,204 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:03:45,215 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8934b453-f11b-4a1e-b759-e961799f9f93'}}
2025-08-12 15:03:45,215 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f2313c28-de01-4b0c-b73c-d54766d5f5d6'}}
2025-08-12 15:03:45,216 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:45,216 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:45,242 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7413732e-ca99-4d06-9100-c8238bce1ed0'}}
2025-08-12 15:03:45,243 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:03:45,401 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:03:45,439 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '85e8f820-f097-491b-b181-b8cf154b327f'}}
2025-08-12 15:03:45,440 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:45,702 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9c71bf4f-29dd-45f0-8938-f5af3a40ca2e'}}
2025-08-12 15:03:45,703 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:45,712 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:45,713 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:45,921 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2d02b744-f2ec-4be7-88d2-620a4aa36245'}}
2025-08-12 15:03:45,924 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:46,102 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:46,106 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:46,913 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'e51fdb46-60ca-4f1e-8ea0-781712927ea8'}}
2025-08-12 15:03:46,915 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:03:47,262 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'bc265f80-62ff-404a-bf15-9a8090e5e562'}}
2025-08-12 15:03:47,262 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2aeffb4d-a486-4791-a749-44c823a8df62'}}
2025-08-12 15:03:47,263 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:47,263 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:47,286 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5c578c7e-d444-46ac-983b-d9ad43166a65'}}
2025-08-12 15:03:47,288 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:03:47,485 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9829ef98-6a19-4064-b611-6ffbd4dd640d'}}
2025-08-12 15:03:47,486 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:47,749 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0dc159fd-7596-409b-8f39-6d70385d904b'}}
2025-08-12 15:03:47,751 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:47,757 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:47,758 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:47,998 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '063793e5-04b3-4774-be67-7a99299f4150'}}
2025-08-12 15:03:48,000 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:48,160 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:48,161 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:48,694 - INFO - 翻译成功 - 字段: markssecprogr_a_chart_content, 原文长度: 493, 译文长度: 1653
2025-08-12 15:03:48,698 - INFO - 字段 markssecprogr_a_chart_content 翻译完成并通过验证
2025-08-12 15:03:48,752 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:03:48,761 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:48,798 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '84fbfef8-d7b7-43a1-b58a-147c8d1aa2db'}}
2025-08-12 15:03:48,800 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:48,809 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:48,811 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:48,961 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '9f913db6-04b7-43db-9134-df8179a7e21f'}}
2025-08-12 15:03:48,962 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:03:49,005 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:03:49,046 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '84e9d486-7ff2-4454-bf73-11d546d0ed64'}}
2025-08-12 15:03:49,047 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:49,186 - INFO - 记录 731 无需翻译
2025-08-12 15:03:49,187 - INFO - 处理进度: 4/488
2025-08-12 15:03:49,187 - INFO - 开始翻译记录 ID: 730
2025-08-12 15:03:49,204 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:03:49,253 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:49,254 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:49,309 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '73b3f21f-5be3-44a6-8ebe-d2da22f4a972'}}
2025-08-12 15:03:49,309 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b884f9cf-9aad-49b6-8882-29020726ff5b'}}
2025-08-12 15:03:49,310 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:49,310 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:49,329 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '955841bf-c3ac-4345-9371-02665bd7b4ef'}}
2025-08-12 15:03:49,331 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:03:49,524 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c92b1002-9509-4ca4-b920-e89216563066'}}
2025-08-12 15:03:49,525 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:49,699 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:03:49,968 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:03:50,003 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '89212805-02fe-40ad-adeb-2480c8055bd9'}}
2025-08-12 15:03:50,004 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:03:50,310 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:03:50,315 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:03:50,331 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:03:50,358 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ff0a5adb-4122-43ae-acf9-e765171e29db'}}
2025-08-12 15:03:50,359 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:50,367 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '39100623-cc88-4699-9399-b937523955d3'}}
2025-08-12 15:03:50,368 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:50,371 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd7a36a6d-e9f8-400b-a490-ad910b2ca23f'}}
2025-08-12 15:03:50,372 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:50,525 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:03:50,571 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '10bfe3f1-2d7d-4ed6-8be8-fc6883f1daf2'}}
2025-08-12 15:03:50,573 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:50,855 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '62f4b0c1-57b2-43d4-afb3-e32c8915874d'}}
2025-08-12 15:03:50,857 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:50,867 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:50,872 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:51,086 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '90c8fbf0-559d-4e1f-84c9-60dba1e8ef03'}}
2025-08-12 15:03:51,087 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:51,312 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:51,314 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:52,055 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '55d861d6-bfd4-4477-a3df-679c32e23019'}}
2025-08-12 15:03:52,058 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:03:52,399 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8e79159b-d7ec-4e23-9ce0-4350695a1f6f'}}
2025-08-12 15:03:52,400 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:52,405 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '36dd448b-0e99-476e-9512-e3ccf8a09594'}}
2025-08-12 15:03:52,406 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:52,414 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'aecd0f6c-d7a8-42f6-9dcb-5b8c4c68e7c4'}}
2025-08-12 15:03:52,415 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:52,615 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4106073e-bb50-43bd-933a-2febb89e33bc'}}
2025-08-12 15:03:52,616 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:52,892 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a19b359b-de97-4714-b469-eddc8f5cf94e'}}
2025-08-12 15:03:52,893 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:52,914 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:52,915 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:53,122 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '44a444b6-48cb-4e79-a660-390fe0671821'}}
2025-08-12 15:03:53,123 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:53,370 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:53,371 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:53,895 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:03:53,919 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:03:53,937 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '70382c17-5b1e-4cab-92e0-a70862c65a9e'}}
2025-08-12 15:03:53,938 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:53,957 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:53,958 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:54,102 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '363df92d-dd92-4696-98f7-c5686da6dfaf'}}
2025-08-12 15:03:54,103 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:03:54,124 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:03:54,164 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '003d00da-6ff2-43ef-8403-d02816155d27'}}
2025-08-12 15:03:54,165 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:54,397 - INFO - 记录 730 无需翻译
2025-08-12 15:03:54,397 - INFO - 处理进度: 5/488
2025-08-12 15:03:54,397 - INFO - 开始翻译记录 ID: 729
2025-08-12 15:03:54,413 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:03:54,439 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '87dafcbe-94cd-4348-acc4-4aad8dcae3d4'}}
2025-08-12 15:03:54,440 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:54,441 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dd873f5a-3141-4bb3-840d-782f26afc204'}}
2025-08-12 15:03:54,442 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:54,448 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'eb443eec-956e-4422-a0e4-6621690e90bd'}}
2025-08-12 15:03:54,449 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:54,459 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:54,460 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:54,658 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ecd39f52-3659-4da2-a31d-3645a6a1003f'}}
2025-08-12 15:03:54,659 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:55,105 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:03:55,147 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '69ed611b-7e2f-4f12-8da8-59d65d452529'}}
2025-08-12 15:03:55,148 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:55,445 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:03:55,447 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:03:55,452 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:03:55,498 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c9823cdf-fc84-48f0-bb15-ff2b0b0a32f6'}}
2025-08-12 15:03:55,498 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0f7f5cfe-f825-40af-9e6f-9e604fcd3f38'}}
2025-08-12 15:03:55,498 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fdf35e7f-b26f-4aaf-988a-b0ada9ea2b33'}}
2025-08-12 15:03:55,501 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:55,501 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:55,501 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:55,659 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:03:55,700 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ca2d1674-4ee1-4438-91f9-1a6c91a918d9'}}
2025-08-12 15:03:55,701 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:55,986 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3aa14ce8-1eaa-48b7-a9cb-452d87d66126'}}
2025-08-12 15:03:55,987 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:56,000 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:56,001 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:56,209 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '06720841-33dc-411b-b457-02c67d0adede'}}
2025-08-12 15:03:56,211 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:56,521 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:56,525 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:57,183 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'bb1ecb42-9e74-497e-9d5d-f5d691e124cb'}}
2025-08-12 15:03:57,184 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:57,542 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '72604805-6147-48bf-9402-6651055df80f'}}
2025-08-12 15:03:57,543 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:57,547 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8a89e93c-dfc9-4948-af30-b6009dbbd2c0'}}
2025-08-12 15:03:57,547 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b3a0c1e3-db15-4ca9-8e14-ce25a002251d'}}
2025-08-12 15:03:57,548 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:57,548 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:57,739 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6e6f75f9-2e09-4d84-ab3f-b0032b780135'}}
2025-08-12 15:03:57,740 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:03:58,028 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '70c16f07-2449-45bb-87d0-33a156ff6a1b'}}
2025-08-12 15:03:58,029 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:58,043 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:58,044 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:58,250 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4f18e7e8-88bb-4bd0-9d7f-7f5695fddf5d'}}
2025-08-12 15:03:58,252 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:58,581 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:03:58,582 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:59,031 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:03:59,045 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:03:59,068 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6af67534-1a20-4975-a39d-eca8db3f3fd0'}}
2025-08-12 15:03:59,069 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:59,077 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:03:59,078 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:59,228 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '1daf5b67-9c0b-43c5-aed9-8e9697f26c17'}}
2025-08-12 15:03:59,229 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:59,255 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:03:59,291 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '23390da5-67df-490a-970e-a3eef342f21e'}}
2025-08-12 15:03:59,292 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:03:59,582 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a0859ff9-2a35-4a56-8655-727160868219'}}
2025-08-12 15:03:59,583 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:59,586 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e5d1f175-3f9a-44df-b560-fa68d146f4a2'}}
2025-08-12 15:03:59,588 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:59,596 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '52a3006b-a784-47e5-b12b-d97065f86749'}}
2025-08-12 15:03:59,597 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:03:59,606 - INFO - 记录 729 无需翻译
2025-08-12 15:03:59,606 - INFO - 已处理 5 条记录，休息3秒...
2025-08-12 15:03:59,786 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f7ff08a6-7352-48cd-a4f4-fd2942af4b65'}}
2025-08-12 15:03:59,787 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:00,232 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:04:00,271 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '30378d2f-b134-4d9a-ad6b-1f3da5b62967'}}
2025-08-12 15:04:00,272 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:00,586 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:04:00,589 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:04:00,598 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:04:00,644 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '05d5451e-8d69-4814-b44d-0f364c46064c'}}
2025-08-12 15:04:00,644 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f9511c3f-246b-4265-a545-9526585f19b0'}}
2025-08-12 15:04:00,652 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:00,660 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:00,663 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '312bec4b-349b-4dc0-bafb-6919aa5c2b28'}}
2025-08-12 15:04:00,672 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:00,790 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:04:00,843 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '52f82e5f-543a-4cc5-9948-514ed411816e'}}
2025-08-12 15:04:00,845 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:01,119 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '53374f71-34d6-4b04-9ee7-903e3a00e10a'}}
2025-08-12 15:04:01,120 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:01,120 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:01,121 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:01,353 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd02f9722-5bed-47f1-98dd-e69ea4bfd10c'}}
2025-08-12 15:04:01,354 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:02,317 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'f20a392f-4298-44c9-a733-acd3b2837185'}}
2025-08-12 15:04:02,318 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:02,607 - INFO - 处理进度: 6/488
2025-08-12 15:04:02,607 - INFO - 开始翻译记录 ID: 728
2025-08-12 15:04:02,623 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:04:02,670 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:02,671 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:02,701 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0015bf16-8107-4e31-bdb4-289db0559798'}}
2025-08-12 15:04:02,701 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '19f90dc8-581d-400e-970e-3a94e538b720'}}
2025-08-12 15:04:02,702 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:02,702 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:02,716 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fc2cafa3-f5aa-45fa-9c3d-1dc9977eb6e0'}}
2025-08-12 15:04:02,717 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:02,885 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd6447328-5579-4f85-a9bd-e7054f10f043'}}
2025-08-12 15:04:02,886 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:03,156 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:03,156 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3c30d514-7f78-4635-950e-fcd64022bc63'}}
2025-08-12 15:04:03,157 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:03,157 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:03,429 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9bbcb026-3798-43d7-842c-f040527c5c56'}}
2025-08-12 15:04:03,430 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:04,160 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:04:04,162 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:04:04,208 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:04,209 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:04,210 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b6b3b511-e6a5-4fae-8ced-a97398426b71'}}
2025-08-12 15:04:04,211 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:04:04,357 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'ca260b63-d686-499a-856a-cc62a2589433'}}
2025-08-12 15:04:04,359 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:04,436 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:04:04,479 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7d15762c-13d6-46b6-b700-74bfc23314ec'}}
2025-08-12 15:04:04,480 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:04,728 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:04,729 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:04,740 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7721b42b-7783-49ec-80dd-a05e9f7554ad'}}
2025-08-12 15:04:04,741 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:04,746 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '017ec037-e827-491a-b66b-4edc2097a885'}}
2025-08-12 15:04:04,747 - ERROR - 字段 marksthirprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:04,751 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c554cf78-3162-413b-a175-73016ca9b286'}}
2025-08-12 15:04:04,752 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:04,931 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c4463887-3e55-4ee6-bf8d-4b32b61e8bec'}}
2025-08-12 15:04:04,932 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:05,361 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:04:05,399 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'e48fa3a2-645e-4e88-b1fa-73f2d9a82f40'}}
2025-08-12 15:04:05,400 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:05,741 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:04:05,748 - INFO - 翻译字段: marksthirprogr_b_chart_content (类型: JSON)
2025-08-12 15:04:05,752 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:04:05,777 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7d253577-5554-41ec-b36f-99e0582b298f'}}
2025-08-12 15:04:05,778 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:05,781 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '04b6a7e2-25e3-45fb-b15a-c8318de3df15'}}
2025-08-12 15:04:05,782 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:05,790 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cc257b59-ae67-4179-a3c9-20d11c4c3f7f'}}
2025-08-12 15:04:05,791 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:04:05,936 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:04:05,983 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9e964ddb-7652-4b6b-8126-e56b6d5c826e'}}
2025-08-12 15:04:05,986 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:06,251 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:06,252 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '950d765e-c15c-4ea2-a2b6-bb500566b383'}}
2025-08-12 15:04:06,252 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:06,253 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:04:06,518 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '899ce987-8b36-40a5-84b0-03725f69359a'}}
2025-08-12 15:04:06,519 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:06,783 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:06,784 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:07,439 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '7828b03c-3dd6-4fe6-97dc-f7955de3b69f'}}
2025-08-12 15:04:07,441 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:07,786 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:04:07,820 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'bd6b1d18-1141-4f2c-912b-3a6abbc3a2a4'}}
2025-08-12 15:04:07,820 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6bdc6d69-957f-4c92-be84-9b188aa943b8'}}
2025-08-12 15:04:07,821 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:07,821 - WARNING - 字段 marksthirprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:07,830 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '6423c98e-413b-4c86-a251-bedb39410259'}}
2025-08-12 15:04:07,831 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:04:07,834 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:07,835 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:08,033 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5292229d-3e24-490e-92de-71a7d6e71543'}}
2025-08-12 15:04:08,034 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:08,311 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:08,311 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '35534190-7adc-4009-9b7a-179076d9dfbd'}}
2025-08-12 15:04:08,312 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:08,312 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:04:08,562 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1da3332a-0f21-4e70-b37c-f2af16d49304'}}
2025-08-12 15:04:08,563 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:09,308 - INFO - 翻译成功 - 字段: markssecprogr_b_chart_content, 原文长度: 511, 译文长度: 1720
2025-08-12 15:04:09,311 - INFO - 字段 markssecprogr_b_chart_content 翻译完成并通过验证
2025-08-12 15:04:09,314 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:04:09,315 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:04:09,375 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '05e5d6e2-28ec-4006-95b7-e22aeef2fe85'}}
2025-08-12 15:04:09,376 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:09,377 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:09,378 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:04:09,488 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '2020ff3f-46f9-441c-94f3-2ca1f2563c73'}}
2025-08-12 15:04:09,489 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:09,565 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:04:09,600 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1d735010-db92-41c4-b26e-4c5a7c27175a'}}
2025-08-12 15:04:09,601 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:09,858 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5beaad6f-aa80-4df7-a782-bb714b093cf2'}}
2025-08-12 15:04:09,859 - ERROR - 字段 marksthirprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:09,860 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '900b19de-42d8-483d-b4d3-287aaf11234b'}}
2025-08-12 15:04:09,861 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:09,862 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '459fbbc7-722f-4282-9084-ce4bfef37d21'}}
2025-08-12 15:04:09,863 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:04:09,890 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:09,891 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:10,071 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '4bf0ffc6-68e9-49d7-b304-ec9cedf0a546'}}
2025-08-12 15:04:10,071 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:10,312 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:04:10,490 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:04:10,529 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'e194e4a6-9fba-4a5f-8a86-0e97aa08f6a2'}}
2025-08-12 15:04:10,530 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:10,863 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:04:10,865 - INFO - 翻译字段: markssecprogr_a_chart_content (类型: JSON)
2025-08-12 15:04:10,885 - INFO - 记录 717 无需翻译
2025-08-12 15:04:10,885 - INFO - 处理进度: 18/488
2025-08-12 15:04:10,886 - INFO - 开始翻译记录 ID: 716
2025-08-12 15:04:10,903 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:04:10,905 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8d1dad28-8b7c-42fc-85fe-86172462e414'}}
2025-08-12 15:04:10,906 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:10,912 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '60d4d8f8-3462-4965-81e8-698224c61f6c'}}
2025-08-12 15:04:10,913 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:10,938 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '931f6ff1-096d-4222-90e0-38d3003cab1d'}}
2025-08-12 15:04:10,939 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:04:11,087 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:04:11,166 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'bf5d98e7-1a4d-45ac-811c-98e2afe567ed'}}
2025-08-12 15:04:11,175 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:11,431 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '676c7e31-0775-4b89-9d6e-45e0c46f5eee'}}
2025-08-12 15:04:11,432 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:11,433 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:11,433 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:04:11,651 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd51e7582-dfef-4b89-b259-dc441bd01330'}}
2025-08-12 15:04:11,652 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:11,945 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:11,947 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:12,579 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '0544b8c9-dbab-4c72-8be1-2e0148560c60'}}
2025-08-12 15:04:12,580 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:12,947 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'db37d956-db08-46ca-a6d6-d2b463d10ff0'}}
2025-08-12 15:04:12,947 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0582f29d-d97e-426b-adad-0b55b36a77ce'}}
2025-08-12 15:04:12,948 - WARNING - 字段 markssecprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:12,948 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:12,964 - INFO - 记录 728 无需翻译
2025-08-12 15:04:12,965 - INFO - 处理进度: 7/488
2025-08-12 15:04:12,965 - INFO - 开始翻译记录 ID: 727
2025-08-12 15:04:12,974 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '28dcd92e-dcff-45fa-bfd4-c6388c141b4e'}}
2025-08-12 15:04:12,975 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:04:12,979 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:04:13,035 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:13,038 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:13,225 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '783d25f9-a073-4ef4-9d38-c948b4ec160f'}}
2025-08-12 15:04:13,226 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:13,483 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'eb29fe69-2bfe-462d-8ee7-da8beb34204a'}}
2025-08-12 15:04:13,483 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:13,484 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:13,484 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:04:13,688 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd0658d81-38f9-4d80-898b-5249cbde007f'}}
2025-08-12 15:04:13,689 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:14,484 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:04:14,504 - INFO - 记录 711 无需翻译
2025-08-12 15:04:14,504 - INFO - 处理进度: 24/488
2025-08-12 15:04:14,504 - INFO - 开始翻译记录 ID: 710
2025-08-12 15:04:14,575 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:04:14,582 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'aab726cb-9a2f-41bd-8f54-2b0dcf5a837e'}}
2025-08-12 15:04:14,583 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:14,638 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:14,641 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:04:14,651 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '1e16f774-5c00-446b-864d-44159fe9437f'}}
2025-08-12 15:04:14,684 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:14,691 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:04:14,778 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '50b438bc-f131-4ed6-992e-9a8e539830db'}}
2025-08-12 15:04:14,784 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:15,050 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5b9da6a3-5883-4031-a3ac-e57529f721fa'}}
2025-08-12 15:04:15,051 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f6bfb79e-bba7-4454-874a-02f9355d081e'}}
2025-08-12 15:04:15,052 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:15,054 - ERROR - 字段 markssecprogr_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:15,055 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd8ef2c70-c6ca-43bc-81ff-68d65a786d62'}}
2025-08-12 15:04:15,057 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:04:15,113 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:15,114 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:15,268 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'da23f19f-0636-479d-a77a-c00947edd74b'}}
2025-08-12 15:04:15,269 - ERROR - 字段 natalthirteenpointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:15,694 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:04:15,739 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '1be29b62-842d-4e31-88f2-86998f0e2f91'}}
2025-08-12 15:04:15,740 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:16,053 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:04:16,060 - INFO - 翻译字段: markssecprogr_b_chart_content (类型: JSON)
2025-08-12 15:04:16,064 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:04:16,098 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'dbde545f-d68a-448e-8a2b-8ec8a4548048'}}
2025-08-12 15:04:16,100 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:16,101 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '91ae22c1-fc67-437a-8232-7ed62a94877c'}}
2025-08-12 15:04:16,101 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '578f716e-1937-409f-b42d-c08d612863cf'}}
2025-08-12 15:04:16,102 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:16,102 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:04:16,273 - INFO - 翻译字段: current_chart_content (类型: JSON)
2025-08-12 15:04:16,327 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b0a7d39b-3320-4276-a226-e47b75e8fb85'}}
2025-08-12 15:04:16,328 - WARNING - 字段 current_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:16,633 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f05f5da8-09d2-4322-81d5-a430ebba9c70'}}
2025-08-12 15:04:16,634 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:16,683 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:16,684 - WARNING - 字段 content API翻译失败，准备重试第2次
2025-08-12 15:04:16,822 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fc3faaef-0c0f-4a21-a677-a666ff0ea5d6'}}
2025-08-12 15:04:16,823 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:17,163 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:17,166 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:17,780 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'c6e1037f-d002-44d1-92c4-a29c01faa215'}}
2025-08-12 15:04:17,781 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:18,148 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a2687dbb-ccdc-49fb-b0b1-ea62aaa58e22'}}
2025-08-12 15:04:18,148 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8058e1ba-7063-4c4a-acfa-45de626d6cbe'}}
2025-08-12 15:04:18,148 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '384255c7-cace-4452-9fe7-870e50c38437'}}
2025-08-12 15:04:18,151 - WARNING - 字段 markssecprogr_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:18,151 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:04:18,151 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:18,171 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:04:18,213 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:18,215 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:18,377 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '78412a41-49f2-40af-9b17-6bf374520338'}}
2025-08-12 15:04:18,378 - WARNING - 字段 current_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:18,681 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f89064d9-6def-43e7-9bab-62c5339335f2'}}
2025-08-12 15:04:18,683 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:18,737 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:18,738 - ERROR - 字段 content API翻译重试2次后仍失败
2025-08-12 15:04:18,875 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1ab22ca3-ccf9-497f-b9cd-c50df3c4a244'}}
2025-08-12 15:04:18,878 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:19,688 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:04:19,728 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ef92984c-18c3-4c72-850c-903a2b92c68b'}}
2025-08-12 15:04:19,729 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:19,744 - INFO - 翻译字段: new_content (类型: JSON)
2025-08-12 15:04:19,790 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:19,790 - WARNING - 字段 new_content API翻译失败，准备重试第1次
2025-08-12 15:04:19,835 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'eb72ec39-1d1f-4885-847e-20bc01a0cc9c'}}
2025-08-12 15:04:19,838 - ERROR - 字段 lunarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:19,880 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:04:19,914 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '52aab21e-0bf1-4832-8d03-81b378155d0d'}}
2025-08-12 15:04:19,915 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:04:20,192 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2dc6e3d2-a1cc-48bf-837b-e4ab614d8b15'}}
2025-08-12 15:04:20,192 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f7427022-c5fa-4e23-93e1-b785a21365a2'}}
2025-08-12 15:04:20,192 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2e33b65a-0915-44fd-9e75-e6a1631fbe00'}}
2025-08-12 15:04:20,193 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:04:20,193 - ERROR - 字段 markssecprogr_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:20,193 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:20,262 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:20,263 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:20,413 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f949e3be-e908-4e81-8d59-33a0fd4b061d'}}
2025-08-12 15:04:20,414 - ERROR - 字段 current_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:20,843 - INFO - 翻译字段: solarreturn_chart_content (类型: JSON)
2025-08-12 15:04:20,878 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'c376b48c-ffcf-4e12-9446-8b9c4e111b87'}}
2025-08-12 15:04:20,879 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:21,193 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:04:21,194 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:04:21,194 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:04:21,239 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ef0a1b72-3c62-4bd5-b690-fde04d9bbd84'}}
2025-08-12 15:04:21,239 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '56b35f98-9a4d-4a6c-b554-ca724d375096'}}
2025-08-12 15:04:21,239 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '20904d27-64f5-40a9-8396-b10208412f82'}}
2025-08-12 15:04:21,241 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:21,241 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:21,241 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:21,420 - INFO - 翻译字段: chart_content_markdown (类型: Markdown)
2025-08-12 15:04:21,468 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '70f5e6a1-bfa5-42ef-90e4-f1e105c64d2d'}}
2025-08-12 15:04:21,471 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第1次
2025-08-12 15:04:21,770 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd40effb5-1db1-400c-942d-455bb155f0d9'}}
2025-08-12 15:04:21,771 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:21,844 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:21,845 - WARNING - 字段 new_content API翻译失败，准备重试第2次
2025-08-12 15:04:21,960 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7c5604f6-4643-46fd-876c-cfb53418a94b'}}
2025-08-12 15:04:21,961 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:04:22,301 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:22,303 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:22,935 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '7ceb5897-050d-4a82-ba67-a4c131a4295d'}}
2025-08-12 15:04:22,937 - WARNING - 字段 solarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:23,282 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c484003a-edf8-4e97-b771-ddb4669e80f3'}}
2025-08-12 15:04:23,282 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd184c071-fac0-46ad-8a79-39066a7417c6'}}
2025-08-12 15:04:23,283 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:23,283 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:23,286 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '601cab9c-8bcb-4fee-a012-f9f78dc6e65f'}}
2025-08-12 15:04:23,287 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:23,309 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:04:23,348 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:23,349 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:23,518 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '2b53bbb4-a2c1-4649-9548-570b7ad4cab6'}}
2025-08-12 15:04:23,520 - WARNING - 字段 chart_content_markdown API翻译失败，准备重试第2次
2025-08-12 15:04:23,815 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fbe7ff81-e481-47c4-b6f1-4f3af52cb702'}}
2025-08-12 15:04:23,817 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:23,897 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:23,899 - ERROR - 字段 new_content API翻译重试2次后仍失败
2025-08-12 15:04:24,010 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd7555a4d-ebd3-4c32-9e32-619bdbe8b01a'}}
2025-08-12 15:04:24,012 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:04:24,820 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:04:24,870 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '40744c09-ac4e-4643-80cd-440706689f27'}}
2025-08-12 15:04:24,874 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:24,900 - INFO - 翻译字段: transit_chart_content (类型: JSON)
2025-08-12 15:04:24,968 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:24,969 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:24,995 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '265eaa49-4efb-4091-975e-d91c6bc1493f'}}
2025-08-12 15:04:24,996 - ERROR - 字段 solarreturn_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:25,013 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:04:25,052 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ffb9ffe0-b1d1-4a2c-a273-1214327106c2'}}
2025-08-12 15:04:25,053 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:25,334 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9c21d505-513c-4d0d-ac25-208f258e00e7'}}
2025-08-12 15:04:25,334 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '85399fac-043d-47a4-94b2-0f42a2f5ac1d'}}
2025-08-12 15:04:25,335 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1d5c2894-21a8-4699-ab60-03b39fdb67b3'}}
2025-08-12 15:04:25,335 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:25,335 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:25,336 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:25,387 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:25,388 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:25,563 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3224206c-c17f-4a42-9140-aca2dfbd61b8'}}
2025-08-12 15:04:25,564 - ERROR - 字段 chart_content_markdown API翻译重试2次后仍失败
2025-08-12 15:04:25,997 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:04:26,042 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '4040a52b-8e58-4181-b238-24a42cf5d003'}}
2025-08-12 15:04:26,044 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:26,336 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:04:26,337 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:04:26,339 - INFO - 翻译字段: timesmidpointthirprogr_chart_content (类型: JSON)
2025-08-12 15:04:26,377 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'b5aed4f5-acf7-4650-94e4-b8ff17517832'}}
2025-08-12 15:04:26,378 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:26,382 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0171d3b5-ff01-4089-a041-0b77155d53db'}}
2025-08-12 15:04:26,382 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5b519538-01a0-4fd0-88be-33f3e62d618a'}}
2025-08-12 15:04:26,383 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:26,383 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:26,567 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:04:26,644 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '57aab175-b627-40ea-b593-06f1fc3ac89b'}}
2025-08-12 15:04:26,647 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:26,916 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f5327ee4-ed4f-4cef-a045-1574fcb7a9fd'}}
2025-08-12 15:04:26,919 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:27,017 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:27,019 - WARNING - 字段 transit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:27,103 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1224aef5-ddc7-45af-b672-ceed484ef2b6'}}
2025-08-12 15:04:27,105 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:27,424 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:27,426 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:28,099 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'ba4ceb72-9156-44cb-9498-441e14c1e721'}}
2025-08-12 15:04:28,101 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:28,419 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '81b9838b-bef2-4445-8fc9-dd0b3ee8d6a1'}}
2025-08-12 15:04:28,419 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:28,423 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e5ed7ce2-3048-46d4-afe0-413ee0360706'}}
2025-08-12 15:04:28,424 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a09fb2bf-e8cc-4155-ba7c-64cefc479d67'}}
2025-08-12 15:04:28,424 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:28,425 - WARNING - 字段 timesmidpointthirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:28,443 - INFO - 记录 727 无需翻译
2025-08-12 15:04:28,443 - INFO - 处理进度: 8/488
2025-08-12 15:04:28,443 - INFO - 开始翻译记录 ID: 726
2025-08-12 15:04:28,478 - INFO - 记录 726 无需翻译
2025-08-12 15:04:28,478 - INFO - 处理进度: 9/488
2025-08-12 15:04:28,478 - INFO - 开始翻译记录 ID: 725
2025-08-12 15:04:28,493 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:04:28,536 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:28,537 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:28,688 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3dc96cf1-babe-4235-9538-ef05e2ca5aae'}}
2025-08-12 15:04:28,689 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:28,964 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '45053010-c336-4c8b-9dba-131ea67197a8'}}
2025-08-12 15:04:28,967 - ERROR - 字段 compositesecondary_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:29,074 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:29,075 - ERROR - 字段 transit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:29,148 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fd06f0fe-c2de-42e3-922d-ea3bc21ceaae'}}
2025-08-12 15:04:29,149 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:29,970 - INFO - 翻译字段: marks_a_chart_content (类型: JSON)
2025-08-12 15:04:30,010 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5996e01e-409d-4aa4-bcc2-ccfb8bca431b'}}
2025-08-12 15:04:30,011 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:30,075 - INFO - 翻译字段: combination_chart_content (类型: JSON)
2025-08-12 15:04:30,118 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:30,119 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:30,144 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '00a8af95-6703-4d2e-bdb3-3bd9eb184601'}}
2025-08-12 15:04:30,145 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:30,150 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:04:30,188 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f6bc62be-39c5-4ee4-bbed-67e4d7cbcf72'}}
2025-08-12 15:04:30,189 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:30,466 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '822084e9-0ae0-4c5d-9896-286cd14a0302'}}
2025-08-12 15:04:30,467 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f76f0596-65e3-4c6c-90d4-b8137d746cb3'}}
2025-08-12 15:04:30,467 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:30,468 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:30,476 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '1a743562-d5cc-41bc-b7a1-80e1daf7f501'}}
2025-08-12 15:04:30,477 - ERROR - 字段 timesmidpointthirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:30,621 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:30,622 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:30,733 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a452325b-835e-437d-a0da-57ae8921b283'}}
2025-08-12 15:04:30,734 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:31,146 - INFO - 翻译字段: developed_chart_content (类型: JSON)
2025-08-12 15:04:31,187 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'ee73afa5-5ad9-478e-890f-ecb8f6d30b87'}}
2025-08-12 15:04:31,187 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:31,468 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:04:31,468 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:04:31,480 - INFO - 翻译字段: timesmidpointsecprogr_chart_content (类型: JSON)
2025-08-12 15:04:31,509 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e79f6208-55d9-4cd5-ae63-018c8f34ffc4'}}
2025-08-12 15:04:31,510 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:31,515 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '33200e95-9ff6-4847-a549-9afe2deb73d0'}}
2025-08-12 15:04:31,516 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0a2a809a-dd90-4d2d-88b0-21745ca1ec74'}}
2025-08-12 15:04:31,516 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:31,517 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:31,739 - INFO - 翻译字段: comparision_b_chart_content (类型: JSON)
2025-08-12 15:04:31,781 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '37b7b2ec-a318-4bf5-8cc4-330c0a58bf6c'}}
2025-08-12 15:04:31,782 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:32,059 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '51581aa9-f644-431a-81db-7d00c186939c'}}
2025-08-12 15:04:32,061 - WARNING - 字段 marks_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:32,110 - INFO - 翻译成功 - 字段: timesmidpoint_chart_content, 原文长度: 625, 译文长度: 2062
2025-08-12 15:04:32,112 - WARNING - 字段 timesmidpoint_chart_content 翻译结果JSON验证失败: 翻译结果仍包含中文字符，尝试调用模型进行JSON修复
2025-08-12 15:04:32,158 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:32,159 - WARNING - 字段 combination_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:32,228 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'bb4a1f91-abad-4c0a-a90e-216a7ed73ccf'}}
2025-08-12 15:04:32,229 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:32,665 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:32,666 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:33,233 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '75d759a8-e22e-47ea-a610-65bb845a078e'}}
2025-08-12 15:04:33,234 - WARNING - 字段 developed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:33,549 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'e0cea1a2-224d-4c7c-80fb-2ff29b1a0171'}}
2025-08-12 15:04:33,550 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:33,554 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '49c5f3a1-b5be-4942-8312-71c30b3fee6e'}}
2025-08-12 15:04:33,555 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9245a374-4ef7-41d3-a722-6d30929259cf'}}
2025-08-12 15:04:33,555 - WARNING - 字段 timesmidpointsecprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:33,556 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:33,687 - INFO - 记录 725 无需翻译
2025-08-12 15:04:33,688 - INFO - 处理进度: 10/488
2025-08-12 15:04:33,688 - INFO - 开始翻译记录 ID: 724
2025-08-12 15:04:33,705 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:04:33,759 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:33,760 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:33,821 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '677e5ebd-9ffb-4887-a658-e7bd6ab436d6'}}
2025-08-12 15:04:33,822 - WARNING - 字段 comparision_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:34,107 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9dae9d7a-d197-43d4-b0a1-5368aa69b2b4'}}
2025-08-12 15:04:34,108 - ERROR - 字段 marks_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:34,201 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:34,202 - ERROR - 字段 combination_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:34,270 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cef03fca-9cd9-4dad-89f2-29c9c9be7cf7'}}
2025-08-12 15:04:34,271 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:35,110 - INFO - 翻译字段: marks_b_chart_content (类型: JSON)
2025-08-12 15:04:35,148 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7892e303-8b62-4005-b2a4-b810e527135b'}}
2025-08-12 15:04:35,149 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:35,203 - INFO - 翻译字段: thirdprogressed_chart_content (类型: JSON)
2025-08-12 15:04:35,280 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:04:35,318 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:35,319 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:35,356 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '9693c5d8-4f73-4741-926d-2976382ac2e1'}}
2025-08-12 15:04:35,357 - ERROR - 字段 developed_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:35,374 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '022d8f68-e6ee-40ba-8928-74465d839f53'}}
2025-08-12 15:04:35,375 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:35,600 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '13d71192-9aaa-4678-b853-f3e383db8e6b'}}
2025-08-12 15:04:35,600 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'ee643bc8-7d94-409d-a0be-772d55cd25c5'}}
2025-08-12 15:04:35,600 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7e348c85-123f-4bbb-b4c9-f5d7738bb0a4'}}
2025-08-12 15:04:35,601 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:35,601 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:35,601 - ERROR - 字段 timesmidpointsecprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:35,802 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:35,803 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:35,865 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3ac8cc16-0158-426b-ad41-4458c28b5f83'}}
2025-08-12 15:04:35,866 - ERROR - 字段 comparision_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:36,358 - INFO - 翻译字段: smalllimit_chart_content (类型: JSON)
2025-08-12 15:04:36,398 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '93233ba9-ddbf-4836-be11-158862c60b43'}}
2025-08-12 15:04:36,399 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:36,601 - INFO - 翻译字段: title (类型: 普通文本)
2025-08-12 15:04:36,601 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:04:36,603 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:04:36,648 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'aed31118-f16c-4d1e-9b6f-eae9fb77bb14'}}
2025-08-12 15:04:36,648 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8b801694-80fe-40f8-986f-a214b796d0ee'}}
2025-08-12 15:04:36,648 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '7ba4324e-e451-487a-8bde-072eea6c5398'}}
2025-08-12 15:04:36,649 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:36,649 - WARNING - 字段 title API翻译失败，准备重试第1次
2025-08-12 15:04:36,649 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:36,872 - INFO - 翻译字段: compositeThirprogr_chart_content (类型: JSON)
2025-08-12 15:04:36,914 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '15daf566-d6cc-49e3-9307-479fa4a6f586'}}
2025-08-12 15:04:36,917 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:37,215 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '88f7909f-e402-4fd9-af8d-c9a7acf0480f'}}
2025-08-12 15:04:37,218 - WARNING - 字段 marks_b_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:37,358 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:37,359 - WARNING - 字段 thirdprogressed_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:37,410 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cebe14c5-349f-4e52-a9fe-ca99095761cd'}}
2025-08-12 15:04:37,411 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:37,878 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:37,879 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:38,440 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '449968c0-51fd-4bc3-b2c2-db0a95d3bfd6'}}
2025-08-12 15:04:38,441 - WARNING - 字段 smalllimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:38,687 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '07b1462f-520c-47fc-a2b1-737fc98d9af6'}}
2025-08-12 15:04:38,688 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:38,693 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'fd43acb0-9433-4793-aaf4-38def9889f33'}}
2025-08-12 15:04:38,693 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5dc34c7e-6093-44c4-9ecc-2eb4d2e8c508'}}
2025-08-12 15:04:38,694 - WARNING - 字段 title API翻译失败，准备重试第2次
2025-08-12 15:04:38,694 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:38,895 - INFO - 记录 724 无需翻译
2025-08-12 15:04:38,896 - INFO - 已处理 10 条记录，休息3秒...
2025-08-12 15:04:38,970 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'cd0e387e-000e-4e70-b778-1d49e6938842'}}
2025-08-12 15:04:38,973 - WARNING - 字段 compositeThirprogr_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:39,261 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '641acf4b-bc37-42f7-b99a-5870793460e8'}}
2025-08-12 15:04:39,262 - ERROR - 字段 marks_b_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:39,399 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:39,400 - ERROR - 字段 thirdprogressed_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:39,454 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '23475af5-676d-4ef3-b174-2ffcbe83847b'}}
2025-08-12 15:04:39,455 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:40,265 - INFO - 翻译字段: marksthirprogr_a_chart_content (类型: JSON)
2025-08-12 15:04:40,304 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '9bcd15b8-c725-477b-9e36-a6a1f350ea0c'}}
2025-08-12 15:04:40,306 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:40,400 - INFO - 翻译字段: secondarylimit_chart_content (类型: JSON)
2025-08-12 15:04:40,446 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:40,447 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:40,455 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:04:40,483 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'd2241f19-c684-4d2f-8ac9-146282347f93'}}
2025-08-12 15:04:40,484 - ERROR - 字段 smalllimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:40,492 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a2eb278d-1745-4fbe-9533-0f2d3dcead3f'}}
2025-08-12 15:04:40,493 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:40,741 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'd101ec72-11f2-4365-8560-ca3e3d155db5'}}
2025-08-12 15:04:40,742 - ERROR - 字段 title API翻译重试2次后仍失败
2025-08-12 15:04:40,747 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '065fee48-a529-40e5-aa31-a0ee5896a6fa'}}
2025-08-12 15:04:40,748 - ERROR - 字段 secondarylimit_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:40,757 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c05c33b6-c20c-4bf3-9777-94ebea53dfb2'}}
2025-08-12 15:04:40,759 - ERROR - 字段 nataltwelvepointer_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:41,035 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'c69c5d1c-bf60-40f0-9a48-c828bba09a6f'}}
2025-08-12 15:04:41,036 - ERROR - 字段 compositeThirprogr_chart_content API翻译重试2次后仍失败
2025-08-12 15:04:41,487 - INFO - 翻译字段: nataltwelvepointer_chart_content (类型: JSON)
2025-08-12 15:04:41,540 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': '0aabf94c-da72-4b98-9b2f-e73683eec4f2'}}
2025-08-12 15:04:41,543 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:41,748 - INFO - 翻译字段: lunarreturn_chart_content (类型: JSON)
2025-08-12 15:04:41,759 - INFO - 翻译字段: natalthirteenpointer_chart_content (类型: JSON)
2025-08-12 15:04:41,798 - INFO - 记录 713 无需翻译
2025-08-12 15:04:41,800 - INFO - 处理进度: 22/488
2025-08-12 15:04:41,801 - INFO - 开始翻译记录 ID: 712
2025-08-12 15:04:41,829 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:04:41,833 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'f9db9262-515d-4945-9f6e-ef3fe8ab835a'}}
2025-08-12 15:04:41,835 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:41,853 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '3a3ac984-b785-4009-9d38-138f60583323'}}
2025-08-12 15:04:41,855 - WARNING - 字段 natalthirteenpointer_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:41,879 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '8f3c87c0-6b7d-40e1-b2c0-27c0feb13129'}}
2025-08-12 15:04:41,880 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:04:41,899 - INFO - 处理进度: 11/488
2025-08-12 15:04:41,899 - INFO - 开始翻译记录 ID: 723
2025-08-12 15:04:41,916 - INFO - 翻译字段: content (类型: 富文本)
2025-08-12 15:04:41,994 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:04:41,998 - WARNING - 字段 content API翻译失败，准备重试第1次
2025-08-12 15:04:42,039 - INFO - 翻译字段: compositesecondary_chart_content (类型: JSON)
2025-08-12 15:04:42,098 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '0cb8f8f2-4368-412f-8bc8-fb1ba42778e3'}}
2025-08-12 15:04:42,099 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第1次
2025-08-12 15:04:42,351 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '60fe80d0-9a4a-4bd9-93d9-aae8430e8542'}}
2025-08-12 15:04:42,352 - WARNING - 字段 marksthirprogr_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:42,489 - ERROR - 腾讯翻译API返回错误: 未知错误
2025-08-12 15:04:42,491 - WARNING - 字段 secondarylimit_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:42,533 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': '5cf4c763-db3b-48a7-ade0-ba9b4b457570'}}
2025-08-12 15:04:42,534 - WARNING - 字段 compositesecondary_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:43,750 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'MissingParameter', 'Message': 'The request is missing a required parameter `Version`.'}, 'RequestId': 'ca181a0c-3c3e-42fa-aeb8-9c6a189ffb7e'}}
2025-08-12 15:04:43,771 - WARNING - 字段 nataltwelvepointer_chart_content API翻译失败，准备重试第2次
2025-08-12 15:04:44,037 - ERROR - 腾讯翻译API返回错误: 未知错误, 完整响应: {'Response': {'Error': {'Code': 'InvalidParameter', 'Message': 'Url key and value should be splited by `=`.'}, 'RequestId': 'a4f36b2d-4874-438b-aec3-3e9da2b0b4f3'}}
2025-08-12 15:04:44,113 - WARNING - 字段 lunarreturn_chart_content API翻译失败，准备重试第2次
2025-08-12 15:06:10,402 - INFO - ============================================================
2025-08-12 15:06:10,402 - INFO - 开始翻译 corpus_en 表中从 content 字段开始的所有字段
2025-08-12 15:06:10,402 - INFO - 开始时间: 2025-08-12 15:06:10.402347
2025-08-12 15:06:10,402 - INFO - ============================================================
2025-08-12 15:06:10,488 - INFO - 数据库连接成功
2025-08-12 15:06:10,488 - INFO - 重试配置: JSON字段=2 次, 其他字段=2 次; 超时=120s; 使用星火大模型翻译API
2025-08-12 15:06:11,204 - INFO - 找到 5 条需要翻译的记录
2025-08-12 15:06:11,204 - INFO - 预览前 5 条需要翻译的记录：
2025-08-12 15:06:11,205 - INFO - ID: 733, 包含中文的字段: 
2025-08-12 15:06:11,205 - INFO - ID: 732, 包含中文的字段: 
2025-08-12 15:06:11,206 - INFO - ID: 731, 包含中文的字段: timesmidpoint_chart_content(JSON)
2025-08-12 15:06:11,206 - INFO - ID: 730, 包含中文的字段: timesmidpoint_chart_content(JSON)
2025-08-12 15:06:11,207 - INFO - ID: 729, 包含中文的字段: solararc_chart_content(JSON)
2025-08-12 15:08:10,055 - INFO - 找到 488 条需要翻译的记录
2025-08-12 15:08:10,056 - INFO - 开始翻译 488 条记录
2025-08-12 15:08:10,056 - INFO - 错误日志将记录到: /Users/<USER>/Downloads/test/corpus_en/translation_errors.txt
2025-08-12 15:08:10,056 - INFO - 处理进度: 1/488
2025-08-12 15:08:10,056 - INFO - 开始翻译记录 ID: 733
2025-08-12 15:08:10,096 - INFO - 记录 733 无需翻译
2025-08-12 15:08:10,097 - INFO - 处理进度: 2/488
2025-08-12 15:08:10,097 - INFO - 开始翻译记录 ID: 732
2025-08-12 15:08:10,139 - INFO - 记录 732 无需翻译
2025-08-12 15:08:10,139 - INFO - 处理进度: 3/488
2025-08-12 15:08:10,140 - INFO - 开始翻译记录 ID: 731
2025-08-12 15:08:10,159 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:08:10,203 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:10,204 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:08:12,251 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:12,254 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:08:14,304 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:14,307 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:08:15,333 - INFO - 记录 731 无需翻译
2025-08-12 15:08:15,334 - INFO - 处理进度: 4/488
2025-08-12 15:08:15,334 - INFO - 开始翻译记录 ID: 730
2025-08-12 15:08:15,354 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:08:15,399 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:15,401 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:08:17,446 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:17,448 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:08:19,496 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:19,499 - ERROR - 字段 timesmidpoint_chart_content API翻译重试2次后仍失败
2025-08-12 15:08:20,516 - INFO - 记录 730 无需翻译
2025-08-12 15:08:20,517 - INFO - 处理进度: 5/488
2025-08-12 15:08:20,517 - INFO - 开始翻译记录 ID: 729
2025-08-12 15:08:20,536 - INFO - 翻译字段: solararc_chart_content (类型: JSON)
2025-08-12 15:08:20,570 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:20,572 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第1次
2025-08-12 15:08:22,613 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:22,615 - WARNING - 字段 solararc_chart_content API翻译失败，准备重试第2次
2025-08-12 15:08:24,658 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:24,661 - ERROR - 字段 solararc_chart_content API翻译重试2次后仍失败
2025-08-12 15:08:25,695 - INFO - 记录 729 无需翻译
2025-08-12 15:08:25,695 - INFO - 已处理 5 条记录，休息3秒...
2025-08-12 15:08:28,700 - INFO - 处理进度: 6/488
2025-08-12 15:08:28,701 - INFO - 开始翻译记录 ID: 728
2025-08-12 15:08:28,727 - INFO - 翻译字段: comparision_a_chart_content (类型: JSON)
2025-08-12 15:08:28,763 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:28,764 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第1次
2025-08-12 15:08:30,809 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:30,812 - WARNING - 字段 comparision_a_chart_content API翻译失败，准备重试第2次
2025-08-12 15:08:32,853 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:32,855 - ERROR - 字段 comparision_a_chart_content API翻译重试2次后仍失败
2025-08-12 15:08:33,862 - INFO - 翻译字段: timesmidpoint_chart_content (类型: JSON)
2025-08-12 15:08:33,905 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:33,907 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第1次
2025-08-12 15:08:35,966 - ERROR - API请求失败: 401 Client Error: Unauthorized for url: https://spark-api.xf-yun.com/v3.1/chat
2025-08-12 15:08:35,967 - WARNING - 字段 timesmidpoint_chart_content API翻译失败，准备重试第2次
2025-08-12 15:08:36,287 - INFO - 数据库连接已关闭
2025-08-12 15:11:15,925 - INFO - ============================================================
2025-08-12 15:11:15,925 - INFO - 开始翻译 corpus_en 表中从 content 字段开始的所有字段
2025-08-12 15:11:15,925 - INFO - 开始时间: 2025-08-12 15:11:15.925373
2025-08-12 15:11:15,925 - INFO - ============================================================
2025-08-12 15:11:16,000 - INFO - 数据库连接成功
2025-08-12 15:11:16,000 - INFO - 重试配置: JSON字段=2 次, 其他字段=2 次; 超时=120s; 使用星火大模型翻译API
2025-08-12 15:11:16,574 - INFO - 找到 5 条需要翻译的记录
2025-08-12 15:11:16,574 - INFO - 预览前 5 条需要翻译的记录：
2025-08-12 15:11:16,577 - INFO - ID: 733, 包含中文的字段: 
2025-08-12 15:11:16,578 - INFO - ID: 732, 包含中文的字段: 
2025-08-12 15:11:16,579 - INFO - ID: 731, 包含中文的字段: timesmidpoint_chart_content(JSON)
2025-08-12 15:11:16,580 - INFO - ID: 730, 包含中文的字段: timesmidpoint_chart_content(JSON)
2025-08-12 15:11:16,580 - INFO - ID: 729, 包含中文的字段: solararc_chart_content(JSON)
    